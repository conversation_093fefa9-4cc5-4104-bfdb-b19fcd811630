<template>
  <div class="px-30 pt-7.5 h-full">
    <template v-if="loading">
      <!-- 骨架屏 -->
      <div class="space-y-7.5 flex flex-col items-center relative">
        <Loading :visible="loading" :data="[thinking]" @update:visible="router.replace('/analysis')" />
        <!-- Profile Card 骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 relative animate-pulse w-1/2">
          <!-- 头像和基本信息 -->
          <div class="flex items-start gap-7.5">
            <div class="w-25 h-25 rounded-full bg-gray-200/30 dark:bg-gray-600/30"></div>
            <div class="flex-1 space-y-4">
              <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-1/3"></div>
              <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
              <div class="flex flex-wrap gap-2.5">
                <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-28"></div>
                <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
              </div>
            </div>
            <div class="flex gap-3">
              <div class="w-10 h-10 rounded-full bg-gray-200/30 dark:bg-gray-600/30"></div>
              <div class="w-10 h-10 rounded-full bg-gray-200/30 dark:bg-gray-600/30"></div>
            </div>
          </div>

          <!-- 统计数据 -->
          <div class="mt-7.5 grid grid-cols-4 gap-7.5">
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
          </div>
        </div>

        <!-- Most Cited Papers 骨架屏 -->
        <div class="w-full bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 animate-pulse min-h-80">
          <div class="h-10 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-1/3 mx-auto mb-7.5"></div>
          <div class="flex justify-center gap-7.5 h-[calc(100%-4.5rem)]">
            <div class="w-[45%] p-7.5 rounded-2xl bg-gray-200/10 dark:bg-gray-600/10 flex flex-col">
              <div class="space-y-4">
                <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/6"></div>
                </div>
              </div>
              <div class="mt-auto space-y-4">
                <div class="h-20 bg-gray-200/20 dark:bg-gray-600/20 rounded-lg w-full"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/3"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                </div>
              </div>
            </div>
            <div class="w-[45%] p-7.5 rounded-2xl bg-gray-200/10 dark:bg-gray-600/10 flex flex-col">
              <div class="space-y-4">
                <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/6"></div>
                </div>
              </div>
              <div class="mt-auto space-y-4">
                <div class="h-20 bg-gray-200/20 dark:bg-gray-600/20 rounded-lg w-full"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/3"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="githubData">
      <div class="space-y-7.5" v-if="githubData && !loading">
        <!-- GitHub Profile Card -->
        <GitHubProfileCard
          :profile="githubProfileData"
          @share-click="showPopup = true"
        />

        <motion.div
          :initial="{ opacity: 0, y: 10 }"
          :animate="{ opacity: 1, y: 0 }"
          class="bg-[#FBF8F7] dark:bg-[#141415] rounded-2xl f-cer pt-7.5 pb-10 flex-col"
        >
          <div class="clash-semibold text-8 text-[#25324B] dark:text-white max-w-547px text-center">
            Compare with other GitHub developers
          </div>
          <div class="text-base text-[#515865] dark:text-[#7A7A7A] mt-4">
            By clicking Compare you agree to our <a href="/terms" target="_blank" class="text-primary-100 hover:underline">terms of service</a>
          </div>
          <motion.div
            :initial="{ opacity: 0, y: 10 }"
            :animate="{ opacity: 1, y: 0 }"
            :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
            class="f-cer mt-7.5 mb-7"
          >
            <div
              class="custom-input border rounded-full bg-white dark:bg-[#141415] border-black dark:border-[#3a3a3a] min-h-16 w-195 p-1 flex items-center justify-between gap-4 pl-7.5"
            >
              <SearchInput ref="compareInputRef" placeholder="GitHub username" @enter-search="handleCompare" />
              <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleCompare" />
            </div>
          </motion.div>
        </motion.div>

        <Teleport to="body">
          <ShareCardGithub
            ref="shareCardRef"
            :show="showPopup"
            :user="user"
            :income="githubData?.valuation_and_level?.salary_range ? (Array.isArray(githubData.valuation_and_level.salary_range) ? githubData.valuation_and_level.salary_range[0] : parseInt(githubData.valuation_and_level.salary_range.split(' - ')[0].replace(/[$,]/g, ''))) || 220000 : 220000"
            :is-dark="isDark"
            :stats="stats"
            :role-model="roleModel"
            :languages="githubData?.code_contribution?.languages"
            :language-total="githubData?.code_contribution?.total"
            :feature-project="githubData?.feature_project"
            :additions="githubData?.overview?.additions"
            :deletions="githubData?.overview?.deletions"
            :valuation-level="githubData?.valuation_and_level"
            :work-experience="githubData?.overview?.work_experience"
            :most-valuable-p-r="githubData?.most_valuable_pull_request"
            @close="showPopup = false"
            :insights-items="items"
          />
        </Teleport>

        <!-- 隐藏的分享卡片用于自动生成OG图片 -->
        <!-- 使用正确的隐藏方式：transform: translateX(-100%) -->
        <div
          v-if="githubData && !ogImageGenerated"
          class="hidden-render-container"
          :style="{
            position: 'fixed',
            top: '0',
            left: '0',
            transform: 'translateX(-100%)',
            zIndex: '-1',
            pointerEvents: 'none'
          }"
        >
          <ShareCardGithub
            ref="hiddenShareCardRef"
            :show="true"
            :user="user"
            :income="githubData?.valuation_and_level?.salary_range ? (Array.isArray(githubData.valuation_and_level.salary_range) ? githubData.valuation_and_level.salary_range[0] : parseInt(githubData.valuation_and_level.salary_range.split(' - ')[0].replace(/[$,]/g, ''))) || 220000 : 220000"
            :is-dark="isDark"
            :stats="stats"
            :role-model="roleModel"
            :languages="githubData?.code_contribution?.languages"
            :language-total="githubData?.code_contribution?.total"
            :feature-project="githubData?.feature_project"
            :additions="githubData?.overview?.additions"
            :deletions="githubData?.overview?.deletions"
            :valuation-level="githubData?.valuation_and_level"
            :work-experience="githubData?.overview?.work_experience"
            :most-valuable-p-r="githubData?.most_valuable_pull_request"
            @close="() => {}"
            :insights-items="items"
          />
        </div>

        <div class="grid grid-cols-2 gap-7.5">
          <!-- overview -->
          <ReportCard icon="research" :title="'Overview'" :card-id="'overview'" @share-popup="showPopup = true">
            <SegmentTable :items="insightsItems" />
            <div>
              <div class="text-4 font-600 leading-12">Total Code Contribution</div>
              <div class="fx-cer justify-between gap-2">
                <div
                  class="w-[265px] h-[148px] custom-bg bg-[url(@/assets/image/Group2x1.png)] dark:bg-[url(@/assets/image/Group2xdark.png)] bg-no-repeat bg-contain bg-left-top p-3 flex flex-col justify-between"
                  style="border-radius: 4px;"
                >
                  <div class="fx-cer gap-1">
                    <span class="fx-cer justify-center w-6 h-6 rounded-1 bg-[#D0D5E3]"
                      ><SvgIcon name="add1" /></span
                    ><span class="text-[#5F6D94] dark:text-[#A5AEC6] font-bold">Additions</span>
                  </div>
                  <div class="text-[#5F6D94] dark:text-[#A5AEC6]" style="font-family: 'Poppins'; font-weight: 600; font-size: 30px;">
                    + {{ formatNumber(githubData.overview.additions) }}
                  </div>
                </div>
                <div
                  class="w-[265px] h-[148px] custom-bg bg-[url(@/assets/image/Group2x.png)] dark:bg-[url(@/assets/image/Group2xdark.png)] bg-no-repeat bg-contain bg-left-top p-3 flex flex-col justify-between"
                  style="border-radius: 4px;"
                >
                  <div class="fx-cer gap-1">
                    <span class="fx-cer justify-center w-6 h-6 rounded-1 bg-[#F1D5CB]"
                      ><SvgIcon name="trash-bin" /></span
                    ><span class="text-[#CB7C5D] dark:text-[#B28383] font-bold">Deletions</span>
                  </div>
                  <div class="text-[#CB7C5D] dark:text-[#B28383]" style="font-family: 'Poppins'; font-weight: 600; font-size: 30px;">
                    - {{ formatNumber(githubData.overview.deletions) }}
                  </div>
                </div>
              </div>
            </div>
          </ReportCard>

          <!-- Recent Activity -->
          <ReportCard icon="yearly" :title="'Recent Activity (Last 31 Days)'" :card-id="'Recent-Activity'" @share-popup="showPopup = true">
            <div class="flex flex-col">
              <div class="h-[337px]">
                <BarChart :activity-data="githubData.activity" />
              </div>
            </div>
          </ReportCard>

          <!-- Featured Projects -->
          <ReportCard
            icon="project"
            :title="'Featured Project'"
            :x="-50"
            :card-id="'Featured-Project'"
            @share-popup="showPopup = true"
          >
            <div v-if="githubData.feature_project" class="flex flex-col gap-5">
              <div
                class="w-[545px] min-h-[152px] border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-4 py-5 rounded-1 dark:bg-[#222222] dark:text-white"
              >
                <div
                  class="font-3.4 font-700 leading-12 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] dark:text-white"
                >
                  <a :href="githubData.feature_project.url" target="_blank" class="hover:underline hover:text-[#CB7C5D] transition-colors">
                    {{ githubData.feature_project.name }}
                  </a>
                </div>
                <div class="text-3.5 font-400 leading-6 text-[#4D4846] dark:text-white line-clamp-5">
                  {{ githubData.feature_project.description }}
                </div>
              </div>
              <div class="fx-cer justify-between gap-2">
                <div
                  class="relative w-[173px] h-[80px] rounded-1 p-3 bg-[#F6F2F1] dark:bg-[#292929] dark:text-white"
                >
                  <div style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 24px;">{{ githubData.feature_project.stargazerCount.toLocaleString() }}</div>
                  <div class="text-[#969696] text-3">Stars</div>
                  <div
                    class="absolute fx-cer justify-center right-3 top-3 h-6 w-6 rounded-1 bg-[#EEDFD9]"
                  >
                    <SvgIcon name="stars" />
                  </div>
                </div>
                <div
                  class="relative w-[173px] h-[80px] rounded-1 p-3 bg-[#F6F2F1] dark:bg-[#292929] dark:text-white"
                >
                  <div style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 24px;">{{ githubData.feature_project.forkCount.toLocaleString() }}</div>
                  <div class="text-[#969696] text-3">Forks</div>
                  <div
                    class="absolute fx-cer justify-center right-3 top-3 h-6 w-6 rounded-1 bg-[#EEDFD9]"
                  >
                    <SvgIcon name="forks" />
                  </div>
                </div>
                <div
                  class="relative w-[173px] h-[80px] rounded-1 p-3 bg-[#F6F2F1] dark:bg-[#292929] dark:text-white"
                >
                  <div style="font-family: 'Poppins'; font-weight: 500; font-size: 24px;">{{ githubData.feature_project.monthly_trending > 0 ? 'Yes' : 'No' }}</div>
                  <div class="text-[#969696] text-3">Monthly Trending</div>
                  <div
                    class="absolute fx-cer justify-center right-3 top-3 h-6 w-6 rounded-1 bg-[#EEDFD9]"
                  >
                    <SvgIcon name="trending" />
                  </div>
                </div>
              </div>
              <div class="fx-cer justify-between gap-2">
                <div
                  class="relative w-[264px] h-[80px] rounded-1 p-3 bg-[#F6F2F1] dark:bg-[#292929] dark:text-white"
                >
                  <div style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 24px;">{{ githubData.feature_project.contributors }}</div>
                  <div class="text-[#969696] text-3">Contributors</div>
                  <div
                    class="absolute fx-cer justify-center right-3 top-3 h-6 w-6 rounded-1 bg-[#EEDFD9]"
                  >
                    <SvgIcon name="man" />
                  </div>
                </div>
                <div
                  class="relative w-[264px] h-[80px] rounded-1 p-3 bg-[#F6F2F1] dark:bg-[#292929] dark:text-white"
                >
                  <div style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 24px;">{{ githubData.feature_project.used_by }}</div>
                  <div class="text-[#969696] text-3">Used by</div>
                  <div
                    class="absolute fx-cer justify-center right-3 top-3 h-6 w-6 rounded-1 bg-[#EEDFD9]"
                  >
                    <SvgIcon name="working" />
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="flex items-center justify-center h-full">
              <div class="text-center text-gray-500 dark:text-gray-400">
                <div class="text-lg font-semibold mb-2">No Featured Project</div>
                <div class="text-sm">This user doesn't have a featured project available</div>
              </div>
            </div>
          </ReportCard>

          <!-- Closest Collaborator -->
          <ReportCard
            icon="closet"
            :title="'Programming Languages'"
            :card-id="'Programming-Languages'"
            @share-popup="showPopup = true"
          >
            <div class="flex flex-col h-full">
              <GithubDonut :languages="githubData.code_contribution.languages" :total="githubData.code_contribution.total" />
            </div>
          </ReportCard>

          <!-- Top Projects Contributed To  -->
          <ReportCard
            icon="contributed"
            :title="'Top Projects Contributed To'"
            :card-id="'Top-Projects-Contributed'"
            :x="-50"
            @share-popup="showPopup = true"
          >
            <div v-if="cardList.length === 1" class="h-full">
              <div
                class="flex flex-col h-full bg-[#FAF2EF] p-4 rounded-1 dark:bg-[#292929] dark:text-white"
              >
                <div class="fx-cer justify-between border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E]">
                  <div class="fx-cer gap-4 h-[90px]">
                    <img :src="cardList[0].avatar" alt="" class="rounded-full w-[50px] h-[50px]" />
                    <div class="text-4.5 font-600">
                      <a :href="cardList[0].url" target="_blank" class="hover:underline hover:text-[#CB7C5D] transition-colors">
                        {{ cardList[0].title }}
                      </a>
                    </div>
                  </div>
                  <div
                    class="fx-cer gap-2 border border-[#CB7C5D] dark:border-[#3E3E3E] py-1 px-2 rounded-1 bg-[#FBEAE3] dark:bg-[#323232]"
                  >
                    <span class="bg-[#CB7C5D] rounded-full p-1">
                      <SvgIcon name="favorites" class-name="text-[#ffffff]" />
                    </span>
                    <span class="text-[#CB7C5D]" style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 16px;">{{ cardList[0].stars.toLocaleString() }}</span><span class="text-black dark:text-[#7A7A7A]">Stars</span>
                  </div>
                </div>
                <div class="flex-1 flex flex-col justify-between">
                  <div
                    class="break-words whitespace-normal text-4 text-[#1E1F25] mt-4 line-clamp-5 dark:text-white"
                  >
                    {{ cardList[0].description }}
                  </div>
                  <div class="text-4 text-[#CB7C5D] dark:text-[#C6C6C6] text-center">
                    <span style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 64px;">{{ cardList[0].prs.toLocaleString() }}</span>PRs
                  </div>
                </div>
              </div>
            </div>

            <!-- Multiple projects -->
            <div v-else-if="cardList.length > 0" class="flex gap-2 h-full overflow-hidden">
              <div
                v-for="(item, index) in cardList.slice(0, 3)"
                :key="index"
                class="flex flex-col h-full bg-[#FAF2EF] p-4 rounded-1 dark:bg-[#292929] dark:text-white flex-1 min-w-0"
              >
                <div
                  class="flex flex-col items-center gap-1 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] dark:bg-[#292929] dark:text-white pb-2"
                >
                  <img :src="item.avatar" alt="" class="rounded-full w-[50px] h-[50px] flex-shrink-0" />
                  <div class="text-4.5 font-600 text-center min-w-0 w-full">
                    <a :href="item.url" target="_blank" class="hover:underline hover:text-[#CB7C5D] transition-colors block truncate" :title="item.title">
                      {{ item.title }}
                    </a>
                  </div>
                  <div
                    class="fx-cer gap-2 border border-[#CB7C5D] dark:border-[#3E3E3E] py-1 px-2 rounded-1 bg-[#FBEAE3] dark:bg-[#323232] flex-shrink-0"
                  >
                    <span class="bg-[#CB7C5D] rounded-full p-1 flex-shrink-0">
                      <SvgIcon name="favorites" class-name="text-[#ffffff]" />
                    </span>
                    <span class="text-[#CB7C5D] whitespace-nowrap" style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 16px;">{{ item.stars.toLocaleString() }}</span><span class="text-black dark:text-[#7A7A7A] whitespace-nowrap">Stars</span>
                  </div>
                </div>
                <div class="flex-1 flex flex-col justify-between min-h-0">
                  <div
                    class="break-words whitespace-normal text-4 text-[#1E1F25] mt-4 line-clamp-5 dark:text-white overflow-hidden"
                  >
                    {{ item.description }}
                  </div>
                  <div class="text-4 text-[#CB7C5D] dark:text-[#C6C6C6] text-center flex-shrink-0">
                    <span style="font-family: 'UDC 1.04'; font-weight: 700; font-size: 64px;">{{ item.prs.toLocaleString() }}</span>PRs
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty state -->
            <div v-else class="flex items-center justify-center h-full">
              <div class="text-center text-gray-500 dark:text-gray-400">
                <div class="text-lg font-semibold mb-2">This dev hasn't contribute something cool</div>
                <div class="text-sm">No significant project contributions found</div>
              </div>
            </div>
          </ReportCard>

          <!-- Most Valuable Pull Request  -->
          <ReportCard
            icon="pull"
            :title="'Most Valuable Pull Request'"
            :card-id="'Most-Valuable-Pull-Request'"
            :x="-50"
            @share-popup="showPopup = true"
          >
            <div class="flex flex-col h-full p-4 gap-4">
              <div
                class="border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-4 py-5 rounded-1 dark:bg-[#292929] dark:text-white"
              >
                <div
                  class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-6 pb-3 dark:text-white"
                >
                  <a :href="githubData.most_valuable_pull_request.url" target="_blank" class="hover:underline hover:text-[#CB7C5D] transition-colors">
                    {{ githubData.most_valuable_pull_request.title }}
                  </a>
                </div>
                <div class="text-3.5 font-400 leading-6 text-[#4D4846] py-3 dark:text-white">
                  {{ githubData.most_valuable_pull_request.impact }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  Repository: {{ githubData.most_valuable_pull_request.repository }}
                </div>
              </div>

              <div class="bg-[#FAF2EF] px-4 py-5 rounded-1 dark:bg-[#292929] dark:text-white">
                <div
                  class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-6 pb-3 fx-cer gap-2 text-4.5 dark:text-white"
                >
                  <SvgIcon name="impact" class="text-8" /> Impact
                </div>
                <div class="text-3.5 font-400 leading-6 text-[#CB7C5D] py-3 dark:text-white">
                  <span class="text-5 font-700 text-[#CB7C5D]">+{{ formatNumber(githubData.most_valuable_pull_request.additions) }}</span> additions, 
                  <span class="text-5 font-700 text-[#CB7C5D]">-{{ formatNumber(githubData.most_valuable_pull_request.deletions) }}</span> deletions
                </div>
                <div class="text-3.5 font-400 leading-6 text-[#4D4846] dark:text-white">
                  {{ githubData.most_valuable_pull_request.reason }}
                </div>
              </div>
            </div>
          </ReportCard>

          <!-- role model -->
          <ReportCard
            icon="settings"
            :title="'Role Model'"
            :x="-50"
            :card-id="'role-model'"
            @share-popup="showPopup = true"
          >
            <div class="flex flex-col h-full space-y-4">
              <div class="fx-cer gap-4 w-full">
                <Avatar
                  :src="isSelfRoleModel ? githubData.user.avatarUrl : (githubData.role_model?.github ? `https://github.com/${githubData.role_model.github.split('/').pop()}.png` : '/image/avator.png')"
                  :size="70"
                />
                <div class="flex flex-col gap-1 flex-1">
                  <div class="text-2xl font-bold fx-cer justify-between">
                    <span>{{ isSelfRoleModel ? (githubData.user.name || githubData.user.login) : (githubData.role_model?.name || 'Unknown') }}</span>
                    <div class="fx-cer gap-4" v-if="isSelfRoleModel || githubData.role_model?.github">
                      <a
                        class="media-btn wh-8"
                        target="_blank"
                        :href="isSelfRoleModel ? `https://github.com/${githubData.user.login}` : githubData.role_model.github"
                      >
                        <div class="i-ri:github-fill wh-6"></div>
                      </a>
                    </div>
                  </div>
                  <div class="fx-cer gap-1.5 text-sm text-gray-900">
                    <SvgIcon name="verified" />
                    <span v-if="isSelfRoleModel">You are already your own hero!</span>
                    <span v-else>Similarity Score: {{ ((githubData.role_model?.similarity_score || 0) * 100).toFixed(1) }}%</span>
                  </div>
                </div>
              </div>

              <div
                class="border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-4 py-5 rounded-1 dark:bg-[#292929] dark:text-white"
              >
                <div
                  class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-6 pb-3 fx-cer gap-2 text-4.5 dark:text-white"
                >
                  <SvgIcon name="medal" /> Achievement:
                </div>
                <div class="text-3.5 font-400 leading-6 text-[#4D4846] py-3 dark:text-white">
                  {{ githubData.role_model?.achievement || 'GitHub Developer' }}
                </div>
              </div>

              <div
                class="text-3.5 font-400 rounded-1 leading-6 text-[#4D4846] p-3 bg-[#FAF2EF] dark:bg-[#292929] dark:text-white"
                v-if="isSelfRoleModel && githubData.role_model?.reason"
              >
                {{ githubData.role_model.reason }}
              </div>
              <div
                class="text-3.5 font-400 rounded-1 leading-6 text-[#4D4846] p-3 bg-[#FAF2EF] dark:bg-[#292929] dark:text-white"
                v-else-if="isSelfRoleModel"
              >
                Congratulations! You are already your own hero! Your unique development path and contributions have established you as a notable figure in your field. Keep up the excellent work!
              </div>
              <div
                class="text-3.5 font-400 rounded-1 leading-6 text-[#4D4846] p-3 bg-[#FAF2EF] dark:bg-[#292929] dark:text-white"
                v-else-if="githubData.role_model?.reason"
              >
                {{ githubData.role_model.reason }}
              </div>
              <div
                class="text-3.5 font-400 rounded-1 leading-6 text-[#4D4846] p-3 bg-[#FAF2EF] dark:bg-[#292929] dark:text-white text-center text-gray-500"
                v-else
              >
                No detailed information available
              </div>
            </div>
          </ReportCard>

          <!-- Valuation & Level -->
          <ReportCard icon="level-up" :title="'Valuation & Level'" :card-id="'Valuation&Level'" @share-popup="showPopup = true">
            <div class="flex flex-col h-full space-y-4">
              <!-- 上排两个卡片 -->
              <div class="flex gap-4 flex-1">
                <div
                  class="flex-1 bg-[#EBEDF2] bg-[url(/image/circle-quarter.png)] bg-no-repeat bg-[length:100px_122px] bg-bottom-right dark:bg-[#292929] dark:bg-[url(@/assets/image/Group3x.png)] dark:bg-contain dark:bg-bottom flex flex-col justify-between p-4"
                  style="border-radius: 4px;"
                >
                  <div class="fx-cer gap-2 text-3.5 font-400">
                    <span class="bg-[#D0D5E3] w-6 h-6 fx-cer justify-center rounded-1"
                      ><SvgIcon name="growth"
                    /></span>
                    Market Value($)
                  </div>
                  <div>
                    <div class="fx-cer gap-2 text-[#6075AD]" style="font-family: 'Poppins'; font-weight: 600; font-size: 24px;">
                      <span>{{ formatSalaryWithoutSymbol(githubData.valuation_and_level.salary_range) }}</span>
                    </div>
                    <div class="text-3.5 text-[#949291] dark:text-white">Year</div>
                  </div>
                </div>
                <div
                  class="flex-1 bg-[#F1EFEB] bg-[url(/image/circle-quarter.png)] bg-no-repeat bg-[length:100px_122px] bg-bottom-right dark:bg-[#292929] flex flex-col justify-between p-4 dark:bg-[url(@/assets/image/Group3x.png)] dark:bg-contain dark:bg-bottom"
                  style="border-radius: 4px;"
                >
                  <div class="fx-cer gap-2 text-3.5 font-400">
                    <span class="bg-[#DFDEDA] w-6 h-6 fx-cer justify-center rounded-1"
                      ><SvgIcon name="pyramid"
                    /></span>
                    Technical Level
                  </div>
                  <div>
                    <div class="text-[#6A675D]" style="font-family: 'Poppins'; font-weight: 600; font-size: 24px;">
                      {{ githubData.valuation_and_level.level }}
                    </div>
                    <div class="text-3.5 text-[#949291] dark:text-white">
                      Software Engineer
                    </div>
                  </div>
                </div>
              </div>
              <!-- 下排两个卡片 -->
              <div class="flex gap-4 flex-1">
                <div
                  class="flex-1 bg-[#F6F1E7] bg-[url(/image/circle-quarter.png)] bg-no-repeat bg-[length:100px_122px] bg-bottom-right dark:bg-[#292929] flex flex-col justify-between p-4 dark:bg-[url(@/assets/image/Group3x.png)] dark:bg-contain dark:bg-bottom"
                  style="border-radius: 4px;"
                >
                  <div class="fx-cer gap-2 text-3.5 font-400">
                    <span class="bg-[#E5DBC4] w-6 h-6 fx-cer justify-center rounded-1"
                      ><SvgIcon name="ranking"
                    /></span>
                    Industry Ranking
                  </div>
                  <div>
                    <div class="text-[#A38B55]" style="font-family: 'Poppins'; font-weight: 600; font-size: 24px;">
                      {{ typeof githubData.valuation_and_level.industry_ranking === 'number' ? `${(githubData.valuation_and_level.industry_ranking * 100).toFixed(1)}%` : (githubData.valuation_and_level.industry_ranking || '30%') }}
                    </div>
                    <div class="text-3.5 text-[#949291] dark:text-white">Tech</div>
                  </div>
                </div>
                <div
                  class="flex-1 bg-[#F6E6DF] bg-[url(/image/circle-quarter.png)] bg-no-repeat bg-[length:100px_122px] bg-bottom-right dark:bg-[#292929] flex flex-col justify-between p-4 dark:bg-[url(@/assets/image/Group3x.png)] dark:bg-contain dark:bg-bottom"
                  style="border-radius: 4px;"
                >
                  <div class="fx-cer gap-2 text-3.5 font-400">
                    <span class="bg-[#F1D5CB] w-6 h-6 fx-cer justify-center rounded-1"
                      ><SvgIcon name="growth-investing"
                    /></span>
                    Years of Experience
                  </div>
                  <div>
                    <div class="text-[#C97676]" style="font-family: 'Poppins'; font-weight: 600; font-size: 24px;">
                      {{ githubData.overview.work_experience }}
                    </div>
                    <div class="text-3.5 text-[#949291] dark:text-white">Year</div>
                  </div>
                </div>
              </div>
            </div>
          </ReportCard>
        </div>

        <!-- Roast -->
        <ReportCard
          icon="mail"
          :title="'Roast'"
          :card-id="'roast'"
          @share-popup="showPopup = true"
        >
          <div
            class="h-full bg-special-softPeach dark:bg-[#292929] p-4 text-15px text-gray-1000 leading-7"
            style="border-radius: 4px;"
          >
            {{ githubData.roast }}
          </div>
        </ReportCard>

        <motion.div
          class="technical bg-gray-100 dark:bg-gray-800 rounded-4!"
          :initial="{ opacity: 0 }"
          :in-view="{ opacity: 1 }"
          :transition="{ duration: 1, delay: 0.5 }"
          :in-view-options="{ once: true }"
        >
          <div class="text-42px font-bold pl-20 whitespace-nowrap max-w-100">I'm developer</div>
          <div class="max-w-200 text-[#433D3A] dark:text-white pl-10 pr-25" style="font-family: Poppins; font-weight: 400; font-size: 16px; line-height: 30px;">
            Empower the AI community with a generalized agentic platform that seamlessly connects
            devs and talent recruiting staffs, unlocking innovation through deep insights into code,
            and fostering collaboration to accelerate breakthroughs from now.
          </div>
        </motion.div>

        <motion.div
          class="flex flex-col items-center justify-center"
          :initial="{ opacity: 0, y: 10 }"
          :in-view="{ opacity: 1, y: 0 }"
          :transition="{ duration: 1, delay: 0.5 }"
          :in-view-options="{ once: true }"
        >
          <div
            class="font-semibold text-14 max-w-155 text-center clash-semibold mt-9 mb-7.5 text-black dark:text-white"
          >
            Try with other<br />
            AI Developer profile
          </div>
          <motion.div
            :initial="{ opacity: 0, y: 10 }"
            :animate="{ opacity: 1, y: 0 }"
            :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
            class="f-cer mt-7.5 mb-7"
          >
            <div
              class="custom-input border rounded-full bg-white dark:bg-[#141415] border-black dark:border-[#3a3a3a] min-h-16 w-195 p-1 flex items-center justify-between gap-4 pl-7.5"
            >
              <SearchInput ref="searchInputRef" placeholder="Name or Github link" @enter-search="handleSearch" />
              <ActionButton @click="handleSearch" :imgSrc="magicWand" buttonText="Analyze" />
            </div>
          </motion.div>
          <div class="text-sm text-center text-neutral-100 dark:text-white mt-7.5 mb-10">
            By clicking Analyze you agree to our
            <a href="/terms" target="_blank" class="text-primary-100 hover:underline"
              >Terms of Service</a
            >
          </div>
        </motion.div>
      </div>
    </template>
    <InviteCodeModal
      v-if="showInviteModal"
      :error="inviteError"
      :loading="inviteLoading"
      @close="showInviteModal = false"
      @submit="handleSubmitActivationCode"
      @waiting-list="onShowWaitingListModal"
    />
    <WaitingListModal
      v-if="showWaitingListModal"
      @close="showWaitingListModal = false"
      @back="onBackToInviteCode"
    />
    <div
      v-if="inviteSuccess"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
    >
      <div
        class="bg-white dark:bg-[#1a1a1b] rounded-2xl shadow-xl p-8 pt-7 pb-7 w-full max-w-96 relative text-center"
      >
        <div class="text-lg font-semibold text-black dark:text-white mb-6">
          Enter Invite Code
          <button
            class="absolute top-6 right-6 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors text-xl"
            @click="inviteSuccess = false"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 5L5 15M5 5L15 15"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <div class="flex justify-center mb-4">
          <div class="bg-green-500 rounded-full w-12 h-12 flex items-center justify-center mx-auto">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="16" fill="none" />
              <path
                d="M10 17.5L14 21.5L22 13.5"
                stroke="#fff"
                stroke-width="2.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
        <div class="text-xl font-bold text-black dark:text-white mb-2">Verification Successful</div>
        <div class="text-base text-gray-700 dark:text-gray-300 mb-7">
          Congratulations! Your invitation code has been verified successfully
        </div>
        <button
          class="w-full h-12 bg-black dark:bg-white text-white dark:text-black rounded-lg font-semibold text-base transition hover:bg-gray-900 dark:hover:bg-gray-200"
          @click="goHome"
        >
          Ok
        </button>
      </div>
    </div>
  </div>
  <GitHubNotFound
    :visible="!loading && !githubData && !isFetching && hasAttemptedFetch"
    @update:visible="router.replace('/analysis')"
  />
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router'
  import { motion } from 'motion-v'
  import stars from '@/assets/image/stars.png'
  import magicWand from '@/assets/image/magic-wand.png'
  import InviteCodeModal from '@/components/InviteCodeModal.vue'
  import WaitingListModal from '@/components/WaitingListModal.vue'
  import GitHubProfileCard from '@/components/GitHubProfileCard/index.vue'
  import GitHubNotFound from '@/components/GitHubNotFound/index.vue'

  import { ref, watch } from 'vue'
  import { submitActivationCode } from '~/api'
  import BarChart from '@/components/BarChart/index.vue'
  import { DonutChart, GithubDonut, SvgIcon } from '#components'
  import { analyzeGitHubUser as callGitHubAnalyzeAPI } from '~/api'
  import { post } from '~/utils/request'
  import { extractGitHubUsername, uploadFileToS3, getPredictableOgImageUrl, checkOgImageExists } from '~/utils'
  import html2canvas from 'html2canvas-pro'

  definePageMeta({
    middleware: 'auth',
  })

  // 提取 GitHub 用户名的辅助函数
  const extractGitHubUsername = (query: string): string => {
    if (!query) return ''

    // 如果是 GitHub URL，提取用户名
    if (query.includes('github.com/')) {
      const match = query.match(/github\.com\/([^\/\?]+)/)
      return match ? match[1] : ''
    }

    // 如果是纯用户名，直接返回
    return query.trim()
  }

  // 从 URL 获取用户名用于初始 SEO
  const route = useRoute()
  const userParam = route.query.user as string
  const username = userParam ? extractGitHubUsername(userParam) : ''

  // 设置初始 SEO meta（在服务端渲染时生效）
  if (username) {
    const initialTitle = `${username} - GitHub Developer Profile | DINQ`
    const initialDescription = `View ${username}'s GitHub developer profile, code contributions, repositories, and programming skills analysis on DINQ.`

    // 使用可预测的OG图片URL（优先）或GitHub头像（备用）
    const predictableOgImageUrl = getPredictableOgImageUrl(username)
    const fallbackImageUrl = `https://github.com/${username}.png`

    useSeoMeta({
      title: initialTitle,
      description: initialDescription,
      keywords: `${username}, GitHub Developer, Software Engineer, Developer Profile, GitHub Analysis, Code Contribution`,

      // Open Graph - 优先使用可预测的OG图片URL
      ogTitle: initialTitle,
      ogDescription: initialDescription,
      ogType: 'profile',
      ogUrl: `https://dinq.io/github?user=${username}`,
      ogImage: predictableOgImageUrl,

      // Twitter Card - 优先使用可预测的OG图片URL
      twitterCard: 'summary_large_image',
      twitterTitle: initialTitle,
      twitterDescription: initialDescription,
      twitterImage: predictableOgImageUrl,

      // 结构化数据会在useHead中设置
    })

    useHead({
      title: initialTitle,
      meta: [
        {
          name: 'robots',
          content: 'index, follow'
        },
        {
          name: 'theme-color',
          content: '#CB7C5D'
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: `https://dinq.io/github?user=${username}`
        }
      ]
    })
  }

  // 动态 SEO 元数据更新（数据加载后的增强版本）
  const updateSeoMeta = (data: GitHubAnalysisData) => {
    const userName = data.user.name || data.user.login
    const userBio = data.user.bio || ''
    const description = data.description || `${userName} is a developer with ${data.overview.repositories} repositories and ${data.overview.stars} GitHub stars.`

    // 构建技能标签
    const topLanguages = Object.entries(data.code_contribution.languages || {})
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([lang]) => lang)
      .join(', ')

    const keywords = [
      userName,
      'GitHub Developer',
      'Software Engineer',
      'Developer Profile',
      'GitHub Analysis',
      'Code Contribution',
      ...topLanguages.split(', ').filter(Boolean),
      ...(data.user.tags || [])
    ].join(', ')

    useSeoMeta({
      title: `${userName} - GitHub Developer Profile | DINQ`,
      description: description.slice(0, 160), // 限制描述长度
      keywords: keywords,

      // Open Graph
      ogTitle: `${userName} - GitHub Developer Profile`,
      ogDescription: description.slice(0, 160),
      ogImage: ogImageUrl.value || data.user.avatarUrl,
      ogType: 'profile',
      ogUrl: `${process.client ? window.location.origin : 'https://dinq.io'}/github?user=${data.user.login}`,

      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: `${userName} - GitHub Developer Profile`,
      twitterDescription: description.slice(0, 160),
      twitterImage: ogImageUrl.value || data.user.avatarUrl,

      // 结构化数据会在useHead中设置

      // 额外的 meta 标签
      author: userName,
    })

    // 设置页面标题
    useHead({
      title: `${userName} - GitHub Developer Profile | DINQ`,
      meta: [
        {
          name: 'robots',
          content: 'index, follow'
        },
        {
          name: 'theme-color',
          content: '#CB7C5D'
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: `${process.client ? window.location.origin : 'https://dinq.io'}/github?user=${data.user.login}`
        }
      ]
    })
  }

  // 动态更新OG图片meta标签
  const updateSeoMetaWithOgImage = (ogImageUrl: string) => {
    if (!githubData.value) return

    const userName = githubData.value.user.name || githubData.value.user.login
    const description = githubData.value.description || `${userName} is a developer with ${githubData.value.overview.repositories} repositories and ${githubData.value.overview.stars} GitHub stars.`

    // 获取当前域名
    const currentDomain = process.client ? window.location.origin : 'https://dinq.io'
    const pageUrl = `${currentDomain}/github?user=${githubData.value.user.login}`

    // 先移除现有的OG图片meta标签（如果存在）
    if (process.client) {
      const existingOgImage = document.querySelector('meta[property="og:image"]')
      const existingTwitterImage = document.querySelector('meta[name="twitter:image"]')
      if (existingOgImage) existingOgImage.remove()
      if (existingTwitterImage) existingTwitterImage.remove()
    }

    // 使用 useSeoMeta 更新
    useSeoMeta({
      // Open Graph - 使用自定义生成的图片
      ogImage: ogImageUrl,

      // Twitter Card - 使用自定义生成的图片
      twitterImage: ogImageUrl,

      // 确保URL使用当前域名
      ogUrl: pageUrl,
    })

    // 同时使用 useHead 强制更新meta标签
    useHead({
      meta: [
        {
          property: 'og:image',
          content: ogImageUrl,
          key: 'og:image' // 使用key确保唯一性
        },
        {
          name: 'twitter:image',
          content: ogImageUrl,
          key: 'twitter:image' // 使用key确保唯一性
        },
        {
          property: 'og:url',
          content: pageUrl,
          key: 'og:url'
        }
      ]
    })

    // 直接操作DOM确保meta标签被正确设置（作为备用方案）
    if (process.client) {
      nextTick(() => {
        // 确保OG图片meta标签存在且正确
        let ogImageMeta = document.querySelector('meta[property="og:image"]') as HTMLMetaElement
        if (!ogImageMeta) {
          ogImageMeta = document.createElement('meta')
          ogImageMeta.setAttribute('property', 'og:image')
          document.head.appendChild(ogImageMeta)
        }
        ogImageMeta.setAttribute('content', ogImageUrl)

        // 确保Twitter图片meta标签存在且正确
        let twitterImageMeta = document.querySelector('meta[name="twitter:image"]') as HTMLMetaElement
        if (!twitterImageMeta) {
          twitterImageMeta = document.createElement('meta')
          twitterImageMeta.setAttribute('name', 'twitter:image')
          document.head.appendChild(twitterImageMeta)
        }
        twitterImageMeta.setAttribute('content', ogImageUrl)

        console.log('已强制更新DOM中的OG图片meta标签:', ogImageUrl)
      })
    }

    console.log('已更新OG图片meta标签:', ogImageUrl)
  }

  // TypeScript interfaces for GitHub API response
  interface GitHubUser {
    name: string;
    login: string;
    avatarUrl: string;
    bio: string;
    company: string;
    location: string;
    createdAt: string;
    tags: string[];
    url: string;
    id: string;
  }

  interface Overview {
    work_experience: number;
    stars: number;
    repositories: number;
    pull_requests: number;
    issues: number;
    additions: number;
    deletions: number;
  }

  interface ValuationAndLevel {
    level: string;
    salary_range: string | number[];
    total_compensation: string;
    reasoning: string;
    industry_ranking?: string | number;
    growth_potential?: string;
  }

  interface RoleModel {
    name: string;
    github: string;
    similarity_score: number;
    reason: string;
    achievement?: string;
  }

  interface FeatureProject {
    name: string;
    nameWithOwner: string;
    description: string;
    url: string;
    stargazerCount: number;
    forkCount: number;
    tags: string[];
    contributors: number;
    used_by: number;
    monthly_trending: number;
    owner: {
      avatarUrl: string;
    };
  }

  interface MostValuablePR {
    title: string;
    url: string;
    repository: string;
    additions: number;
    deletions: number;
    impact: string;
    reason: string;
  }

  interface ProjectInfo {
    repository: {
      name: string;
      url: string;
      description: string;
      stargazerCount: number;
      owner: {
        avatarUrl: string;
      };
    };
    pull_requests: number;
  }

  interface CodeContribution {
    total: number;
    languages: Record<string, number>;
  }

  interface ActivityData {
    contributions: number;
    pull_requests: number;
    issues: number;
    comments: number;
  }

  interface GitHubAnalysisData {
    user: GitHubUser;
    overview: Overview;
    valuation_and_level: ValuationAndLevel;
    role_model: RoleModel;
    feature_project: FeatureProject | null;
    most_valuable_pull_request: MostValuablePR;
    top_projects: ProjectInfo[];
    code_contribution: CodeContribution;
    activity: Record<string, ActivityData>;
    description: string;
    roast: string;
  }

  const searchInputRef = ref()
  const compareInputRef = ref()
  const { currentUser } = useFirebaseAuth()
  // const route = useRoute() // 已在上面声明
  const router = useRouter()
  // const userParam = route.query.user as string // 已在上面声明
  const isFetching = ref(false)
  const loading = ref(false)
  const showInviteModal = ref(false)
  const inviteError = ref('')
  const inviteLoading = ref(false)
  const inviteSuccess = ref(false)
  const showWaitingListModal = ref(false)
  const showPopup = ref(false)
  const isDark = ref(false)
  const githubData = ref<GitHubAnalysisData | null>(null)
  const usageInfo = ref<any>(null)
  const thinking = ref('')
  const hasAttemptedFetch = ref(false)

  // OG图片相关变量
  const ogImageGenerated = ref(false)
  const ogImageUrl = ref('')
  const shareCardRef = ref()
  const hiddenShareCardRef = ref()

  // 格式化数字，超过1K时显示为K单位，超过1M时显示为M单位
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(0) + 'k'
    }
    return num.toString()
  }

  // 格式化薪资显示，将 - 替换为 ~ 并应用数字格式化
  const formatSalaryDisplay = (salaryRange: string | number[] | number): string => {
    if (!salaryRange) return ''

    // 如果是数字，直接格式化
    if (typeof salaryRange === 'number') {
      return '$' + formatNumber(salaryRange)
    }

    // 如果是数组格式，处理数组
    if (Array.isArray(salaryRange)) {
      return `$${formatNumber(salaryRange[0])} - $${formatNumber(salaryRange[1])}`
    }

    // 如果是字符串格式，先替换分隔符
    let formatted = salaryRange.replace(/-/g, ' - ')

    // 匹配薪资数字并格式化
    // 匹配格式如: "50000 - 80000", "$50000 - $80000", "50,000 - 80,000"
    formatted = formatted.replace(/\$?(\d+(?:,\d{3})*)/g, (match, num) => {
      const cleanNum = parseInt(num.replace(/,/g, ''))
      return '$' + formatNumber(cleanNum)
    })

    return formatted
  }

  // 格式化薪资显示（不包含美元符号）
  const formatSalaryWithoutSymbol = (salaryRange: string | number[] | number): string => {
    if (!salaryRange) return ''

    // 如果是数字，直接格式化
    if (typeof salaryRange === 'number') {
      return formatNumber(salaryRange)
    }

    // 如果是数组格式，处理数组
    if (Array.isArray(salaryRange)) {
      return `${formatNumber(salaryRange[0])} - ${formatNumber(salaryRange[1])}`
    }

    // 如果是字符串格式，先替换分隔符
    let formatted = salaryRange.replace(/-/g, ' - ')

    // 匹配薪资数字并格式化，移除美元符号
    formatted = formatted.replace(/\$?(\d+(?:,\d{3})*)/g, (match, num) => {
      const cleanNum = parseInt(num.replace(/,/g, ''))
      return formatNumber(cleanNum)
    })

    return formatted
  }

  // Computed values for UI display
  const insightsItems = computed(() => {
    if (!githubData.value) return []
    const overview = githubData.value.overview
    return [
      { label: 'GitHub Stars', value: overview.stars.toLocaleString() },
      { label: 'Work Experience', value: overview.work_experience },
      { label: 'Total Issues', value: overview.issues.toLocaleString() },
      { label: 'Repositories', value: overview.repositories.toLocaleString() },
      { label: 'Total PRs', value: overview.pull_requests.toLocaleString() },
    ]
  })

  const githubProfileData = computed(() => {
    if (!githubData.value) return {
      name: '',
      login: '',
      avatar: '',
      bio: '',
      description: '',
      tags: [],
    }
    
    // Add subject (user's name) to description if it doesn't already have one
    const userName = githubData.value.user.name || githubData.value.user.login
    let description = githubData.value.description || ''
    if (description && !description.toLowerCase().includes(userName.toLowerCase())) {
      description = `${userName} ${description}`
    }
    
    return {
      name: userName,
      login: githubData.value.user.login,
      avatar: githubData.value.user.avatarUrl,
      bio: githubData.value.user.bio || '',
      description: description,
      tags: githubData.value.user.tags || [],
    }
  })

  const user = computed(() => {
    if (!githubData.value) return {
      name: '',
      avatar: '',
      role: 'Developer',
      login: '',
      papers: 0,
      citations: 0,
    }
    return {
      name: githubData.value.user.name || githubData.value.user.login,
      avatar: githubData.value.user.avatarUrl,
      role: 'Developer',
      login: githubData.value.user.login,
      bio: githubData.value.user.bio || '',
      papers: githubData.value.overview?.repositories || 0,
      citations: githubData.value.overview?.stars || 0,
    }
  })

  const stats = computed(() => {
    if (!githubData.value) return {
      repositories: 0,
      stars: 0,
      pullRequests: 0,
    }
    return {
      repositories: githubData.value.overview.repositories,
      stars: githubData.value.overview.stars,
      pullRequests: githubData.value.overview.pull_requests,
    }
  })

  const roleModel = computed(() => {
    if (!githubData.value || !githubData.value.role_model) return {
      name: 'Unknown',
      avatar: '/image/avator.png',
      title: 'AI Developer',
      achievement: '',
    }
    
    // 如果是自己就是榜样的情况，返回用户自己的信息
    if (isSelfRoleModel.value) {
      return {
        name: githubData.value.user.name || githubData.value.user.login,
        avatar: githubData.value.user.avatarUrl,
        title: 'AI Developer',
        achievement: githubData.value.role_model?.achievement || githubData.value.role_model?.reason || 'You are already your own hero! Your unique development path and contributions have established you as a notable figure in your field.',
      }
    }
    
    const roleModelData = githubData.value.role_model
    const githubUsername = roleModelData.github ? roleModelData.github.split('/').pop() : null
    
    return {
      name: roleModelData.name || 'Unknown',
      avatar: githubUsername ? `https://github.com/${githubUsername}.png` : '/image/avator.png',
      title: 'AI Developer',
      achievement: roleModelData.achievement || roleModelData.reason || 'GitHub Developer',
    }
  })

  // 判断是否是"自己就是榜样"的情况
  const isSelfRoleModel = computed(() => {
    return githubData.value?.role_model?.name === 'Unknown' || 
           !githubData.value?.role_model?.github ||
           !githubData.value?.role_model?.reason ||
           githubData.value?.role_model?.similarity_score === 1.0
  })



  const items = computed(() => {
    if (!githubData.value) return []
    return [
      { label: 'Github Stars', value: githubData.value.overview.stars },
      { label: 'Repositories', value: githubData.value.overview.repositories },
      { label: 'Pull Requests', value: githubData.value.overview.pull_requests },
    ]
  })

  const cardList = computed(() => {
    if (!githubData.value) return []
    return githubData.value.top_projects.map(project => ({
      title: project.repository.name,
      avatar: project.repository.owner.avatarUrl,
      stars: project.repository.stargazerCount,
      prs: project.pull_requests,
      description: project.repository.description || 'No description available',
      url: project.repository.url,
    }))
  })

  // Watch for route changes to reset state
  watch(() => route.query.user, (newUser) => {
    // Reset invite success state when user parameter changes
    inviteSuccess.value = false
  })

  onMounted(() => {
    // Reset invite success state on mount to prevent stale state
    inviteSuccess.value = false

    // 安全地检测主题模式
    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })

    // Start GitHub analysis if userParam exists
    if (userParam) {
      // 检查是否已有OG图片
      checkExistingOgImage(userParam)
      analyzeGitHubUser(userParam)
    }
  })

  // GitHub API integration functions
  const analyzeGitHubUser = async (username: string) => {
    if (!username.trim()) return

    loading.value = true
    isFetching.value = true
    hasAttemptedFetch.value = true
    thinking.value = `Analyzing GitHub profile for ${username}...`

    console.log('Starting GitHub analysis for:', username)

    try {
      const requestData = { username: username.trim() }
      console.log('Request data:', requestData)
      
      // Use the post function from utils/request which handles URL conversion automatically
      const result = await post('/api/github/analyze', requestData, {
        'Userid': currentUser.value?.uid || '',
      })

      console.log('API Response:', result)
      
      if (result.data.success) {
        console.log('Analysis successful, setting data')
        githubData.value = result.data.data
        usageInfo.value = result.data.usage_info

        // 更新 SEO 元数据
        if (githubData.value) {
          updateSeoMeta(githubData.value)

          // 自动生成OG图片
          nextTick(() => {
            generateOgImage(username)
          })
        }
        } else {
        console.error('Analysis failed:', result.data.error)
        throw new Error(result.data.error || 'Analysis failed')
      }
    } catch (error: any) {
      console.error('Error analyzing GitHub user:', error)
      
      // Handle rate limit errors
      if (error.message?.includes('usage limit') || error.message?.includes('429')) {
        showInviteModal.value = true
        inviteError.value = ''
        inviteLoading.value = false
        return
      }
      
      // Show error to user - alert removed
    } finally {
          loading.value = false
          isFetching.value = false
      thinking.value = ''
      console.log('Analysis completed')
    }
  }

  const handleCompare = () => {
    const searchValue = compareInputRef.value?.searchValue
    console.log('handleCompare', searchValue)
    if (!searchValue?.trim()) return

    // Extract GitHub username from URL or use as-is if it's already a username
    const username = extractGitHubUsername(searchValue)

    // For GitHub comparison, we'll navigate to a GitHub-specific compare page
    router.push({
      path: '/github_compare',
      query: {
        user1: githubData.value?.user?.login,
        user2: username,
      },
    })
  }

  const handleAnalyze = (query?: string) => {
    const search = query || compareInputRef.value?.searchValue
    if (!search.trim()) return

    // Extract GitHub username from URL or use as-is if it's already a username
    const username = extractGitHubUsername(search)

    // Update URL with new user parameter
    router.replace({
      path: '/github',
      query: { user: username },
    })

    // Reset data and start new analysis
    githubData.value = null
    hasAttemptedFetch.value = false
    ogImageGenerated.value = false
    ogImageUrl.value = ''
    analyzeGitHubUser(username)
  }

  // 获取html2canvas的标准配置（复用现有逻辑）
  const getHtml2CanvasConfig = (isDarkMode = false) => ({
    backgroundColor: '#ffffff',
    scale: 2,
    useCORS: true,
    allowTaint: true,
    logging: false,
    imageTimeout: 15000,
    foreignObjectRendering: false,
    scrollX: 0,
    scrollY: 0,
    // 在截图时替换按钮为版权信息
    onclone: (clonedDoc: Document) => {
      const clonedElement = clonedDoc.querySelector('[data-card-id="share-card-github"]')
      if (clonedElement) {
        // 查找按钮容器并替换为版权信息和二维码
        const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
        if (buttonContainer) {
          // 创建右下角信息容器（版权信息+二维码）
          const bottomRightContainer = clonedDoc.createElement('div')
          bottomRightContainer.style.cssText = 'position: absolute; bottom: 8px; right: 16px; display: flex; align-items: center; gap: 8px; z-index: 10;'

          // 添加版权信息
          const copyrightText = clonedDoc.createElement('div')
          copyrightText.style.cssText = 'font-size: 12px; color: #666; font-weight: 500;'
          copyrightText.textContent = 'Generated by DINQ'

          bottomRightContainer.appendChild(copyrightText)

          // 替换按钮容器
          buttonContainer.parentNode?.replaceChild(bottomRightContainer, buttonContainer)
        }

        // 修复毛玻璃效果卡片的背景和样式（复用现有逻辑）
        const glassCards = clonedElement.querySelectorAll('[style*="backdrop-filter: blur(14px)"]')
        glassCards.forEach(card => {
          const cardEl = card as HTMLElement
          if (isDarkMode) {
            cardEl.style.backgroundColor = 'rgba(20, 20, 21, 0.85)'
            cardEl.style.backdropFilter = 'none'
            cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)'
          } else {
            cardEl.style.backgroundColor = 'rgba(255, 255, 255, 0.85)'
            cardEl.style.backdropFilter = 'none'
            cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)'
          }
        })
      }
    }
  })

  // 自动生成OG图片
  const generateOgImage = async (username: string) => {
    if (ogImageGenerated.value || !process.client) return

    try {
      console.log('开始生成OG图片...')

      // 等待Vue的响应式更新完成
      await nextTick()

      const shareCardElement = document.querySelector('[data-card-id="share-card-github"]')
      if (!shareCardElement) {
        console.error('未找到分享卡片元素')
        return
      }

      // 等待图片资源加载完成
      const images = shareCardElement.getElementsByTagName('img')
      const imagePromises = [...images].map(img => {
        if (img.complete) return Promise.resolve()
        return new Promise(resolve => {
          img.onload = resolve
          img.onerror = resolve // 即使图片加载失败也继续
        })
      })
      await Promise.all(imagePromises)

      // 等待自定义字体加载完成
      if (document.fonts) {
        await document.fonts.ready
      }

      // 给浏览器额外的渲染时间
      await new Promise(resolve => setTimeout(resolve, 500))

      console.log('所有资源加载完成，开始截图...')

      // 使用优化后的html2canvas配置，包含图片处理逻辑
      const canvas = await html2canvas(shareCardElement as HTMLElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 1330,
        height: 700,
        logging: true, // 开启调试日志
        imageTimeout: 15000,
        foreignObjectRendering: false,
        scrollX: 0,
        scrollY: 0,
        // 在截图时处理图片和样式兼容性
        onclone: (clonedDoc) => {
          const clonedElement = clonedDoc.querySelector('[data-card-id="share-card-github"]')
          if (clonedElement) {
            // 调整整体卡片尺寸以适应新的OG图片尺寸
            const cardElement = clonedElement as HTMLElement
            cardElement.style.setProperty('width', '1330px', 'important')
            cardElement.style.setProperty('height', '700px', 'important')

            // 调整背景图片的位置到右边，保持原始大小
            const backgroundDiv = cardElement.querySelector('.bg-\\[url\\(\\/image\\/graphbg\\.png\\)\\]') as HTMLElement
            if (backgroundDiv) {
              backgroundDiv.style.setProperty('background-position', 'right top', 'important')
              // 保持原始的 bg-contain 和 bg-no-repeat 设置
            }

            // 调整右侧头像位置：往右20px
            const avatarImg = cardElement.querySelector('img.absolute[class*="top-"][class*="right-"]') as HTMLElement
            if (avatarImg) {
              // 从 right-[230px] 往右20px，变成 right-[210px]
              avatarImg.style.setProperty('right', '210px', 'important')
              // 保持 top-[311px] 不变
              avatarImg.style.setProperty('top', '311px', 'important')
              // 保持原始大小 65px x 65px
              avatarImg.style.setProperty('width', '65px', 'important')
              avatarImg.style.setProperty('height', '65px', 'important')
            }

            // 1. 替换按钮为版权信息
            const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
            if (buttonContainer) {
              const bottomRightContainer = clonedDoc.createElement('div')
              bottomRightContainer.style.cssText = 'position: absolute; bottom: 8px; right: 16px; display: flex; align-items: center; gap: 8px; z-index: 10;'

              const copyrightDiv = clonedDoc.createElement('div')
              copyrightDiv.style.cssText = 'font-size: 12px; color: #666; font-weight: 400;'
              copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'

              const qrCode = clonedDoc.createElement('img')
              qrCode.src = '/image/qrcode.png'
              qrCode.alt = 'QR Code'
              qrCode.style.cssText = 'width: 48px; height: 48px; flex-shrink: 0;'

              bottomRightContainer.appendChild(copyrightDiv)
              bottomRightContainer.appendChild(qrCode)
              buttonContainer.parentNode?.replaceChild(bottomRightContainer, buttonContainer)
            }

            // 2. 替换SVG图标为PNG图片
            const svgIconElements = clonedElement.querySelectorAll('svg.svg-icon')
            svgIconElements.forEach((svgEl) => {
              const svgElement = svgEl as SVGElement
              const useElement = svgElement.querySelector('use')
              if (!useElement) return

              const iconId = useElement.getAttribute('xlink:href') || useElement.getAttribute('href')
              if (!iconId) return

              const imgElement = clonedDoc.createElement('img')

              // 根据图标ID确定要使用的图片
              const iconMappings: Record<string, { src: string; alt: string; className: string }> = {
                '#icon-verified': { src: '/image/sharecard/github-verify.png', alt: 'verified', className: 'w-4 h-4 mt-0.5 flex-shrink-0' },
                '#icon-research': { src: '/image/sharecard/overview.png', alt: 'overview', className: 'w-5 h-5' },
                '#icon-add1': { src: '/image/sharecard/additions.png', alt: 'additions', className: 'w-5 h-5' },
                '#icon-trash-bin': { src: '/image/sharecard/deletions.png', alt: 'deletions', className: 'w-5 h-5' },
                '#icon-project': { src: '/image/sharecard/highlight.png', alt: 'highlight', className: 'w-5 h-5' },
                '#icon-stars': { src: '/image/sharecard/stars.png', alt: 'stars', className: 'w-4 h-4' },
                '#icon-forks': { src: '/image/sharecard/forks.png', alt: 'forks', className: 'w-4 h-4' },
                '#icon-growth': { src: '/image/sharecard/marketvalue.png', alt: 'market value', className: 'w-5 h-5' },
                '#icon-growth-investing': { src: '/image/sharecard/yoe.png', alt: 'yoe', className: 'w-5 h-5' }
              }

              const mapping = iconMappings[iconId]
              if (mapping) {
                imgElement.src = mapping.src
                imgElement.alt = mapping.alt
                imgElement.className = mapping.className
                svgElement.parentNode?.replaceChild(imgElement, svgElement)
              }
            })

            // 3. 修复头像路径
            const avatarImages = clonedElement.querySelectorAll('img[src*="@/assets/image/avator.png"]')
            avatarImages.forEach(img => {
              const imgEl = img as HTMLImageElement
              imgEl.src = '/image/avator.png'
            })

            // 4. 修复自定义背景图片
            const isDarkMode = isDark.value
            const customBgCards = clonedElement.querySelectorAll('.custom-bg')
            customBgCards.forEach((card) => {
              const cardEl = card as HTMLElement

              // 根据卡片内容确定背景图片类型
              const isAdditionsCard = cardEl.textContent?.includes('Additions') || cardEl.querySelector('img[alt="additions"]')
              const isDeletionsCard = cardEl.textContent?.includes('Deletions') || cardEl.querySelector('img[alt="deletions"]')
              const isMarketValueCard = cardEl.textContent?.includes('Market Value') || cardEl.querySelector('img[alt="market value"]')
              const isYoECard = cardEl.textContent?.includes('YoE') || cardEl.querySelector('img[alt="yoe"]')

              // 强制设置背景图片
              if (isDarkMode) {
                cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2xdark.png)', 'important')
              } else {
                if (isAdditionsCard || isMarketValueCard) {
                  cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2x1.png)', 'important')
                } else if (isDeletionsCard || isYoECard) {
                  cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2x.png)', 'important')
                }
              }

              cardEl.style.setProperty('background-repeat', 'no-repeat', 'important')
              cardEl.style.setProperty('background-size', 'contain', 'important')
              cardEl.style.setProperty('background-position', 'left top', 'important')

              // 修复文字颜色以匹配背景图片
              const textElements = cardEl.querySelectorAll('[class*="text-[#"]')
              textElements.forEach(textEl => {
                const textElement = textEl as HTMLElement
                if (isDarkMode) {
                  // 深色模式下的文字颜色
                  if (isAdditionsCard || isMarketValueCard) {
                    // 蓝色系卡片 - 深色模式使用较亮的蓝色
                    if (textElement.classList.contains('text-[#5F6D94]') || textElement.classList.contains('dark:text-[#A5AEC6]')) {
                      textElement.style.setProperty('color', '#A5AEC6', 'important')
                    }
                  } else if (isDeletionsCard || isYoECard) {
                    // 橙色系卡片 - 深色模式使用较亮的橙色
                    if (textElement.classList.contains('text-[#CB7C5D]') || textElement.classList.contains('dark:text-[#B28383]')) {
                      textElement.style.setProperty('color', '#B28383', 'important')
                    }
                  }
                } else {
                  // 浅色模式下的文字颜色
                  if (isAdditionsCard || isMarketValueCard) {
                    // 蓝色系卡片 - 确保文字颜色正确
                    if (textElement.classList.contains('text-[#5F6D94]')) {
                      textElement.style.setProperty('color', '#5F6D94', 'important')
                    }
                  } else if (isDeletionsCard || isYoECard) {
                    // 橙色系卡片 - 确保文字颜色正确
                    if (textElement.classList.contains('text-[#CB7C5D]')) {
                      textElement.style.setProperty('color', '#CB7C5D', 'important')
                    }
                  }
                }
              })
            })

            // 5. 修复毛玻璃效果卡片
            const glassCards = clonedElement.querySelectorAll('[style*="backdrop-filter: blur(14px)"]')
            glassCards.forEach(card => {
              const cardEl = card as HTMLElement
              if (isDarkMode) {
                cardEl.style.backgroundColor = 'rgba(20, 20, 21, 0.85)'
                cardEl.style.backdropFilter = 'none'
                cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)'
              } else {
                cardEl.style.backgroundColor = 'rgba(255, 255, 255, 0.85)'
                cardEl.style.backdropFilter = 'none'
                cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)'
              }
            })

            // 6. 修复高亮卡片的背景颜色和边框
            const highlightCards = clonedElement.querySelectorAll('.border-l-4')
            highlightCards.forEach(card => {
              const cardEl = card as HTMLElement
              if (isDarkMode) {
                // 深色模式样式
                cardEl.style.backgroundColor = '#222222'
                cardEl.style.borderLeftColor = '#654D43'
                cardEl.style.borderLeftWidth = '4px'
                cardEl.style.borderLeftStyle = 'solid'
              } else {
                // 亮色模式样式
                cardEl.style.backgroundColor = '#FAF2EF'
                cardEl.style.borderLeftColor = '#CB7C5D'
                cardEl.style.borderLeftWidth = '4px'
                cardEl.style.borderLeftStyle = 'solid'
              }

              // 修复高亮卡片内的分割线颜色
              const dividers = cardEl.querySelectorAll('.border-b-1')
              dividers.forEach(divider => {
                const dividerEl = divider as HTMLElement
                if (isDarkMode) {
                  dividerEl.style.borderBottomColor = '#3E3E3E'
                } else {
                  dividerEl.style.borderBottomColor = '#F2E8E4'
                }
              })
            })

            // 7. 深色模式下的额外修复
            if (isDarkMode) {
              // 修复所有卡片的边框颜色
              const cardElements = clonedElement.querySelectorAll('.border-gray-200')
              cardElements.forEach(card => {
                (card as HTMLElement).style.borderColor = '#27282D'
              })

              // 修复主卡片的边框颜色
              const mainCard = clonedElement.querySelector('[data-card-id="share-card-github"] > div')
              if (mainCard) {
                (mainCard as HTMLElement).style.borderColor = '#27282D'
              }

              // 修复卡片标题文字颜色
              const titleElements = clonedElement.querySelectorAll('.text-black')
              titleElements.forEach(title => {
                (title as HTMLElement).style.color = '#FAF9F5'
              })
            }
          }
        }
      })

      // 转换为blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/png', 1.0)
      })

      // 生成固定格式的文件名（可预测）
      const fileName = `github-${username}-latest.png`

      // 上传到S3
      const publicUrl = await uploadFileToS3(blob, 'image/png', fileName)

      console.log('OG图片上传成功:', publicUrl)

      // 保存URL到数据库
      await $fetch('/api/github/save-og-image', {
        method: 'POST',
        body: {
          username,
          ogImageUrl: publicUrl
        }
      })

      // 更新本地状态
      ogImageUrl.value = publicUrl
      ogImageGenerated.value = true

      // 动态更新meta标签
      updateSeoMetaWithOgImage(publicUrl)

    } catch (error) {
      console.error('生成OG图片失败:', error)
    }
  }

  // 检查是否已有OG图片
  const checkExistingOgImage = async (username: string) => {
    try {
      const response = await $fetch(`/api/github/og-image/${username}`)
      if (response.success && response.ogImageUrl) {
        ogImageUrl.value = response.ogImageUrl
        ogImageGenerated.value = true
        console.log('找到已有的OG图片:', response.ogImageUrl)

        // 如果找到已有图片，也需要更新meta标签
        if (githubData.value) {
          updateSeoMetaWithOgImage(response.ogImageUrl)
        }
      }
    } catch (error) {
      console.log('未找到已有的OG图片，将在分析完成后生成')
    }
  }

  const handleSearch = (query?: string) => {
    const search = query || searchInputRef.value.searchValue
    if (!search.trim()) return

    // Extract GitHub username from URL or use as-is if it's already a username
    const username = extractGitHubUsername(search)

    // Update URL with new user parameter
    router.replace({
      path: '/github',
      query: { user: username },
    })

    // Reset data and start new analysis
    githubData.value = null
    hasAttemptedFetch.value = false
    analyzeGitHubUser(username)
  }

  // Invite code handling functions
  function onShowWaitingListModal() {
    showInviteModal.value = false
    showWaitingListModal.value = true
  }

  function onBackToInviteCode() {
    showWaitingListModal.value = false
    showInviteModal.value = true
  }

  async function handleSubmitActivationCode(code: string) {
    inviteLoading.value = true
    inviteError.value = ''
    try {
      const res = await submitActivationCode(
        '/api/activation-codes/use',
        { code },
        { headers: { Userid: currentUser.value?.uid || '' } }
      )
      if (res.data?.success) {
        showInviteModal.value = false
        inviteSuccess.value = true
        setTimeout(() => {
          inviteSuccess.value = false
        }, 2000)
      } else {
        inviteError.value = 'Invalid invite code. Please check and try again.'
      }
    } catch (e) {
      inviteError.value = 'Invalid invite code. Please check and try again.'
    } finally {
      inviteLoading.value = false
    }
  }

  function goHome() {
    router.replace('/analysis')
  }
</script>

<style scoped lang="scss">
  .technical {
    background-image: url('~/assets/image/technical.png');
    background-size: 100% 100%;
    background-position: center;
    @apply h-53 bg-no-repeat flex items-center justify-center gap-155px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .dark .technical {
    background-image: url('~/assets/image/technicaldark.png');
    background-size: 100% 100%;
    background-position: center;
    @apply h-53 bg-no-repeat flex items-center justify-center gap-155px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-5 {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-8 {
    display: -webkit-box;
    -webkit-line-clamp: 8;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .success-modal-ok-btn:active {
    background: #222;
  }

  .custom-bg {
    // background: url(../../assets/svg/card-bg.svg);
  }
</style>
