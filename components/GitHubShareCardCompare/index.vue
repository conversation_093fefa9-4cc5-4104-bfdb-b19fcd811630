<template>
  <div v-if="show" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="relative">
      <div
        class="shadow-xl p-8 w-[1000px] h-[570px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1] dark:from-[#141415] dark:to-[#141415] border border-transparent dark:border-[#27282D]"
        style="border-radius: 30px;"
        data-card-id="share-card-github-compare"
      >
        <div>
          <div class="w-full">
            <!-- 主要内容区域 - 预留给具体内容 -->
            <div class="h-[462px] relative">
              <img 
                :src="isDark ? '/image/VS3xl.png' : '/image/VS3xl1.png'" 
                alt="VS Left" 
                class="absolute top-0 left-0 h-full object-contain z-0" />
              <img 
                :src="isDark ? '/image/VS3xr.png' : '/image/VS3xr1.png'" 
                alt="VS Right" 
                class="absolute top-0 right-0 h-full object-contain z-0" />
              <div class="relative z-10">
                <!-- 比较卡片内容 -->
                <div class="grid grid-cols-2 gap-6 -mt-4">
                  <!-- VS 对比图片 -->
                  <div class="absolute left-1/2 top-[40px] z-10" style="transform: translateX(-50%);">
                    <img src="/image/vs-compare.png" alt="VS" class="w-[60px] h-[60px] object-contain" />
                  </div>
                  <!-- 左侧GitHub用户 -->
                  <div class="relative">
                    <div class="absolute" style="left: 360px; top: 30px;">
                      <Avatar :src="user1.user?.avatarUrl || user1.user?.avatar_url || '/image/default-avatar.png'" :size="70" />
                    </div>
                    <div class="absolute" style="right: calc(100% - 350px); top: 35px; width: 300px;">
                      <div class="font-['Poppins'] font-bold text-[24px] leading-[100%] text-right mb-1 truncate">
                        {{ user1.name }}
                      </div>
                      <div class="font-['Poppins'] font-normal text-[14px] leading-[150%] text-right opacity-80 flex items-center justify-end gap-1.5">
                        <img src="/image/sharecard/verified.svg" alt="verified" class="w-4 h-4 flex-shrink-0" />
                        <span class="truncate">{{ user1.user?.bio || 'GitHub Developer' }}</span>
                      </div>
                      <div class="flex flex-wrap gap-2 justify-end mt-3">
                        <Tag 
                          v-for="(tag, index) in getDisplayTags(user1.user?.tags || [])" 
                          :key="index" 
                          variant="share-blue" 
                          :title="tag" 
                          class="max-w-[200px] truncate flex-shrink-0"
                        />
                      </div>
                    </div>
                  </div>
                  <!-- 右侧GitHub用户 -->
                  <div class="relative">
                    <div class="absolute" style="right: 360px; top: 30px;">
                      <Avatar :src="user2.user?.avatarUrl || user2.user?.avatar_url || '/image/default-avatar.png'" :size="70" />
                    </div>
                    <div class="absolute" style="left: calc(100% - 350px); top: 35px; width: 300px;">
                      <div class="font-['Poppins'] font-bold text-[24px] leading-[100%] text-left mb-1 truncate">
                        {{ user2.name }}
                      </div>
                      <div class="font-['Poppins'] font-normal text-[14px] leading-[150%] text-left opacity-80 flex items-center justify-start gap-1.5">
                        <img src="/image/sharecard/verified-brown.svg" alt="verified" class="w-4 h-4 flex-shrink-0" />
                        <span class="truncate">{{ user2.user?.bio || 'GitHub Developer' }}</span>
                      </div>
                      <div class="flex flex-wrap gap-2 justify-start mt-3">
                        <Tag 
                          v-for="(tag, index) in getDisplayTags(user2.user?.tags || [])" 
                          :key="index" 
                          variant="share-orange" 
                          :title="tag" 
                          class="max-w-[200px] truncate flex-shrink-0"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 雷达图部分 -->
                <div class="mt-[130px]">
                  <GitHubRadarChart 
                    :user1="user1 as any" 
                    :user2="user2 as any" 
                    size="small"
                    :hide-legend="true"
                    :is-share-card="true"
                  />
                </div>
              </div>
            </div>

            <!-- 底部分割线和版权信息 -->
            <div class="fx-cer justify-between border-t dark:border-[#323232] h-[70px] mt-4">
              <img
                :src="isDark ? '/image/darklogo2.png' : '/image/newlogo1.png'"
                width="95"
                height="42"
                :alt="isDark ? 'DINQ dark logo' : 'DINQ logo'"
              />
              <div class="flex items-center gap-3" data-action-buttons>
                <!-- Download Button -->
                <button
                  class="fx-cer bg-[#FFFFFF]/60 dark:bg-[#14141580] border border-gray-200 dark:border-[#27282D] rounded-full py-2 px-4 text-black dark:text-white gap-2 transition-all duration-200 select-none dark:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed hover:bg-[#F5F5F5] dark:hover:bg-[#27282D] disabled:hover:bg-[#FFFFFF]/60 dark:disabled:hover:bg-[#14141580] min-h-[40px] cursor-pointer"
                  :disabled="isDownloading"
                  @click="handleDownload"
                  style="backdrop-filter: blur(34px);"
                >
                  <div
                    v-if="isDownloading"
                    class="animate-spin w-4 h-4 border-2 border-gray-400 dark:border-gray-300 border-t-transparent rounded-full pointer-events-none"
                  ></div>
                  <div v-else class="i-material-symbols:download w-4 h-4 pointer-events-none"></div>
                  <span class="text-sm font-medium pointer-events-none">{{ isDownloading ? 'Downloading...' : 'Download' }}</span>
                </button>

                <!-- Share Button -->
                <ShareButton card-id="share-card-github-compare" :is-dark="isDark" variant="transparent" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <button
        class="absolute top-4 right-4 p-2 rounded-full transition-colors bg-transparent dark:bg-[#141415] hover:bg-black/10 dark:hover:bg-white/10"
        @click="$emit('close')"
      >
        <div class="i-carbon:close text-xl text-gray-500"></div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import html2canvas from 'html2canvas-pro'
  import GitHubRadarChart from '../GitHubRadarChart.vue'
  import Avatar from '../Avatar/index.vue'
  import Tag from '../Tag/index.vue'
  import SvgIcon from '../SvgIcon/index.vue'
  import ShareButton from '../ShareButton/index.vue'
  
  interface ShareCardGitHubUser {
    name: string
    user: {
      avatar_url: string
      bio?: string
      tags?: string[]
    }
    code_contribution?: {
      total: number
    }
    overview?: {
      active_days: number
      pull_requests: number
    }
    top_projects?: Array<{
      repository: {
        name: string
        stargazerCount?: number
      }
    }>
    feature_project?: {
      stargazerCount: number
    }
  }

  const props = defineProps<{
    show: boolean
    isDark: boolean
    user1: ShareCardGitHubUser
    user2: ShareCardGitHubUser
  }>()

  const isDownloading = ref(false)

  // 下载功能
  const handleDownload = async () => {
    if (isDownloading.value || !process.client) return

    isDownloading.value = true
    try {
      const elementToCapture = document.querySelector('[data-card-id="share-card-github-compare"]')
      if (!elementToCapture) {
        throw new Error('GitHub compare card element not found')
      }

      // 等待一小段时间确保所有元素都渲染完成
      await new Promise(resolve => setTimeout(resolve, 500))

      // 使用html2canvas-pro，它对现代CSS有更好的支持
      const canvas = await html2canvas(elementToCapture as HTMLElement, {
        backgroundColor: '#ffffff',
        scale: 2,
        useCORS: true,
        allowTaint: true,
        logging: false,
        imageTimeout: 15000,
        // html2canvas-pro有更好的SVG和CSS支持，所以简化配置
        foreignObjectRendering: false,
        // 确保元素完全在视图中
        scrollX: 0,
        scrollY: 0,
        // 在截图时替换按钮为版权信息
        onclone: (clonedDoc) => {
          const clonedElement = clonedDoc.querySelector('[data-card-id="share-card-github-compare"]')
          if (clonedElement) {
            // 查找按钮容器并替换为版权信息和二维码
            const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
            if (buttonContainer) {
              // 创建右下角信息容器（版权信息+二维码）
              const bottomRightContainer = clonedDoc.createElement('div')
              bottomRightContainer.style.cssText = 'position: absolute; bottom: 12px; right: 16px; display: flex; align-items: center; gap: 8px; z-index: 10;'

              // 创建版权信息元素
              const copyrightDiv = clonedDoc.createElement('div')
              copyrightDiv.className = 'text-3.5 font-400'
              copyrightDiv.style.fontSize = '12px'
              copyrightDiv.style.color = props.isDark ? '#7A7A7A' : '#666'
              copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'

              // 创建二维码元素
              const qrCode = clonedDoc.createElement('img')
              qrCode.src = '/image/qrcode.png'
              qrCode.alt = 'QR Code'
              qrCode.style.cssText = 'width: 48px; height: 48px; flex-shrink: 0;'

              // 将版权信息和二维码添加到右下角容器
              bottomRightContainer.appendChild(copyrightDiv)
              bottomRightContainer.appendChild(qrCode)

              // 替换按钮容器为右下角容器
              buttonContainer.parentNode?.replaceChild(bottomRightContainer, buttonContainer)
            }

            // 修复深色模式下的边框颜色和标签样式
            const isDarkMode = props.isDark
            if (isDarkMode) {
              // 修复所有卡片的边框颜色
              const cardElements = clonedElement.querySelectorAll('.border-gray-200')
              cardElements.forEach(card => {
                (card as HTMLElement).style.borderColor = '#27282D'
              })

              // 修复主卡片的边框颜色
              const mainCard = clonedElement.querySelector('[data-card-id="share-card-github-compare"] > div')
              if (mainCard) {
                (mainCard as HTMLElement).style.borderColor = '#27282D'
              }

              // 修复卡片标题文字颜色
              const titleElements = clonedElement.querySelectorAll('.text-black')
              titleElements.forEach(title => {
                (title as HTMLElement).style.color = '#FAF9F5'
              })

              // 修复标签样式 - GitHub compare卡片
              const allTags = clonedElement.querySelectorAll('.tag-component')

              allTags.forEach((tag) => {
                const tagEl = tag as HTMLElement

                // 检查标签的父容器来判断是左侧还是右侧
                let isLeftSide = false
                let isRightSide = false

                // 向上查找父元素，寻找justify-end（左侧）或justify-start（右侧）
                let parent = tagEl.parentElement
                while (parent && parent !== clonedElement) {
                  const parentClasses = parent.className

                  if (parentClasses.includes('justify-end')) {
                    isLeftSide = true
                    break
                  }
                  if (parentClasses.includes('justify-start')) {
                    isRightSide = true
                    break
                  }
                  parent = parent.parentElement
                }

                if (isLeftSide) {
                  // share-blue variant 深色模式样式（左侧用户）
                  tagEl.style.backgroundColor = '#3C4356'
                  tagEl.style.borderColor = '#7F8EB7'
                  tagEl.style.color = '#C2C5CE'
                  tagEl.style.borderWidth = '0.5px'
                  tagEl.style.borderStyle = 'solid'
                } else if (isRightSide) {
                  // share-orange variant 深色模式样式（右侧用户）
                  tagEl.style.backgroundColor = '#413834'
                  tagEl.style.borderColor = '#71635E'
                  tagEl.style.color = '#E1BCAD'
                  tagEl.style.borderWidth = '0.5px'
                  tagEl.style.borderStyle = 'solid'
                }
              })
            } else {
              // 亮色模式下也要确保标签样式正确
              const allTags = clonedElement.querySelectorAll('.tag-component')

              allTags.forEach((tag) => {
                const tagEl = tag as HTMLElement

                // 检查标签的父容器来判断是左侧还是右侧
                let isLeftSide = false
                let isRightSide = false

                let parent = tagEl.parentElement
                while (parent && parent !== clonedElement) {
                  const parentClasses = parent.className

                  if (parentClasses.includes('justify-end')) {
                    isLeftSide = true
                    break
                  }
                  if (parentClasses.includes('justify-start')) {
                    isRightSide = true
                    break
                  }
                  parent = parent.parentElement
                }

                if (isLeftSide) {
                  // share-blue variant 亮色模式样式（左侧用户）
                  tagEl.style.backgroundColor = '#EEF1F9'
                  tagEl.style.borderColor = '#7F8EB7'
                  tagEl.style.color = '#7F8EB7'
                  tagEl.style.borderWidth = '0.5px'
                  tagEl.style.borderStyle = 'solid'
                } else if (isRightSide) {
                  // share-orange variant 亮色模式样式（右侧用户）
                  tagEl.style.backgroundColor = '#FBEAE3'
                  tagEl.style.borderColor = '#CB7C5D'
                  tagEl.style.color = '#CB7C5D'
                  tagEl.style.borderWidth = '0.5px'
                  tagEl.style.borderStyle = 'solid'
                }
              })
            }
          }
        }
      })

      // 创建下载链接
      const link = document.createElement('a')
      link.download = `dinq-github-compare-${Date.now()}.png`
      link.href = canvas.toDataURL('image/png', 1.0)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Download failed:', error)
      // 提供备用方案提示
      alert('Screenshot failed. This might be due to CORS issues with external images. Please try again or contact support.')
    } finally {
      isDownloading.value = false
    }
  }

  // 估算文本宽度的函数
  const estimateTextWidth = (text: string) => {
    // 基于字符数量的简单估算，可以根据实际字体调整
    return text.length * 8 + 20 // 每个字符约8px，加上padding
  }

  // 判断显示哪些标签的函数
  const getDisplayTags = (tags: string[]) => {
    if (!tags || tags.length === 0) return []
    
    // 如果只有1-2个标签，直接返回
    if (tags.length <= 2) return tags.slice(0, 3)
    
    // 计算前三个标签的总宽度
    const firstThreeTags = tags.slice(0, 3)
    const totalWidth = firstThreeTags.reduce((sum, tag) => sum + estimateTextWidth(tag), 0)
    const gapWidth = 8 * 2 // 两个间隙，每个8px (gap-2)
    const totalWidthWithGaps = totalWidth + gapWidth
    
    // 如果总宽度超过440px（容器450px减去一些余量），只显示前两个
    // 调整阈值，因为现在标签可以更宽，间距也更大
    if (totalWidthWithGaps > 440) {
      return tags.slice(0, 2)
    }
    
    return firstThreeTags
  }

  defineEmits(['close'])
</script>

<style scoped>
  .fx-cer {
    display: flex;
    align-items: center;
  }

  .text-3\.5 {
    font-size: 0.875rem;
  }

  .font-400 {
    font-weight: 400;
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
