<template>
  <div>
    <div v-if="loading" class="loading-state">Fetching recommendations...</div>
    <div v-else-if="error" class="error-state">Error: {{ error }}</div>
    <div v-else-if="displayCandidates.length > 0" class="talent-card-container">
      <div class="results-card-bg2"></div>
      <div class="results-card-bg"></div>

      <!-- 导航按钮 -->
      <div
        class="nav-button prev-button"
        @click="prevCandidate"
        :class="{ disabled: currentIndex === 0, hidden: showNetworkModal }"
      >
        <svg width="30" height="30" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div
        class="nav-button next-button"
        @click="nextCandidate"
        :class="{ disabled: currentIndex >= displayCandidates.length - 1, hidden: showNetworkModal }"
      >
        <svg width="30" height="30" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M6 12L10 8L6 4" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>

      <!-- 主卡片 -->
      <div class="results-card">
        <div v-if="currentDisplayCandidate" class="card-content">
          <!-- 1. 头部区域 - 个人基本信息 -->
          <section class="header-section">
            <div class="personal-info">
              <div class="avatar-container">
                <img
                  :src="currentDisplayCandidate.avatarUrl || '/image/avator.png'"
                  :alt="currentDisplayCandidate.name"
                  class="avatar-image"
                  @error="handleImageError"
                />
              </div>
              <div class="basic-info">
                <h2 class="candidate-name">{{ currentDisplayCandidate.name }}</h2>
                <p class="candidate-title">{{ currentDisplayCandidate.positionTitle }}</p>
              </div>
            </div>
            <div class="skills-tags">
              <span
                v-for="skill in currentDisplayCandidate.skills.slice(0, 3)"
                :key="skill"
                class="skill-tag"
              >
                {{ skill }}
              </span>
            </div>
          </section>

          <!-- 2. 研究成果区域 -->
          <section v-if="currentDisplayCandidate.featuredWork" class="research-section">
            <h3 class="research-title">
              <!-- 如果是GitHub仓库，添加链接 -->
              <a 
                v-if="isGitHubRepository(currentDisplayCandidate)"
                :href="getGitHubRepoUrl(currentDisplayCandidate)"
                target="_blank"
                class="hover:text-[#CB7C5D] hover:underline transition-colors cursor-pointer"
              >
                {{ getDisplayTitle(currentDisplayCandidate.featuredWork.title) }}
              </a>
              <!-- 否则显示普通文本 -->
              <span v-else>{{ currentDisplayCandidate.featuredWork.title }}</span>
            </h3>
            <div class="research-meta">
              <div class="conference-info">
                <span class="conference-name">{{ currentDisplayCandidate.featuredWork.venue?.toUpperCase() }} {{ currentDisplayCandidate.featuredWork.year }}</span>
              </div>
              <span class="paper-type">{{ currentDisplayCandidate.featuredWork.type }}</span>
            </div>
          </section>

          <!-- 3. 推荐理由区域 -->
          <section class="recommendation-section">
            <div class="recommendation-content">
              <h3 class="recommendation-title">
                <span class="recommendation-prefix">Why we recommend </span>
                <span class="recommendation-name">{{ currentDisplayCandidate.name ? currentDisplayCandidate.name.split(' ')[0] : 'this candidate' }}</span>
                <span class="recommendation-prefix"> ?</span>
              </h3>
              <ul class="recommendation-list">
                <li v-for="(reason, idx) in currentDisplayCandidate.recommendations" :key="idx" class="recommendation-item">
                  {{ reason }}
                </li>
              </ul>
            </div>

            <!-- 4. 操作区域 -->
            <div class="action-section">
              <button class="btn-analyze" @click="handleAnalyzeAction">
                <img src="~/assets/image/analysis.svg" alt="Analyze" class="btn-icon btn-icon-light" />
                <img src="~/assets/image/analysis2.svg" alt="Analyze" class="btn-icon btn-icon-dark" />
                Analyze
              </button>
              <button class="btn-network" @click="showNetworkModal = true">
                <img src="~/assets/image/button-group.svg" alt="Network" class="btn-icon btn-icon-light" />
                <img src="~/assets/image/button-group2.svg" alt="Network" class="btn-icon btn-icon-dark" />
                Network
              </button>
              <button class="btn-see-another" @click="fetchNewRecommendations">
                <img src="~/assets/image/refresh 1.svg" alt="Refresh" class="btn-icon btn-icon-light" />
                <img src="~/assets/image/refresh2.svg" alt="Refresh" class="btn-icon btn-icon-dark" />
                See Another
              </button>
            </div>
          </section>

          <!-- 评分图表 -->
          <div class="score-chart">
            <ProgressCircle :progress="currentDisplayCandidate.matchScore || 0" />
            <div class="match-score-label">Match Score</div>
          </div>
        </div>
        <div v-else class="empty-state">
          <p>No candidate data available for current selection.</p>
        </div>
      </div>
    </div>
    <div v-else-if="!loading && !error" class="empty-state">
      <p>No recommendations found for your query.</p>
    </div>

    <!-- Network模态框组件 -->
    <NetworkModal
      v-if="currentDisplayCandidate"
      :show="showNetworkModal"
      :currentCandidate="currentDisplayCandidate"
      @update:show="showNetworkModal = $event"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { recommendPapers, type RecommendPapersParams } from '~/api/recommend'; // Adjust path as needed
import type {
  NewRecommendPapersResponse,
  NewRecommendedPaper, // API type for an individual paper with author info
  GitHubData, // API type for GitHub data
  RecommendDataItem, // Wrapper type with data_type
  DisplayCandidate   // Our transformed type for the card
} from '~/api/types'; // Import from api types
import NetworkModal from './index.vue';

// --- Router ---
const router = useRouter();

// --- Props ---
const props = defineProps<{
  candidates?: DisplayCandidate[]
}>()

// --- Component State ---
const rawApiResponse = ref<NewRecommendPapersResponse | null>(null);
const loading = ref(true);
const error = ref<string | null>(null);
const currentIndex = ref(0);
const showNetworkModal = ref(false);

// --- Helper Functions ---
// 格式化数字，添加千分分隔符
const formatNumber = (num: number | string): string => {
  if (typeof num === 'string') {
    const parsedNum = parseInt(num, 10);
    return isNaN(parsedNum) ? num : parsedNum.toLocaleString();
  }
  return num.toLocaleString();
};

// --- API Fetching and Data Transformation ---
const fetchRecommendations = async (query = "LLM Agent") => {
  loading.value = true;
  error.value = null;
  try {
    const params: RecommendPapersParams = {
      query: query,
      first_author_only: 1 // Only get first authors
    };
    rawApiResponse.value = await recommendPapers(params);
    currentIndex.value = 0; // Reset to first card on new data
  } catch (err: any) {
    console.error("Failed to fetch recommendations:", err);
    error.value = err.message || 'Failed to load recommendations.';
    rawApiResponse.value = null; // Clear previous data on error
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  // 只有在没有传入candidates时才自动获取数据
  if (!props.candidates || props.candidates.length === 0) {
    fetchRecommendations();
  } else {
    loading.value = false;
  }
});

const displayCandidates = computed((): DisplayCandidate[] => {
  // 如果有传入的candidates，优先使用
  if (props.candidates && props.candidates.length > 0) {
    return props.candidates;
  }
  
  // 否则使用API响应数据
  if (!rawApiResponse.value || !rawApiResponse.value.data) {
    return [];
  }
  return rawApiResponse.value.data.map((item: RecommendDataItem): DisplayCandidate => {
    // 根据data_type处理不同类型的数据 (paper 和 github)
    if (item.data_type === 'github') {
      const githubData = item.data as GitHubData;
      
      // Handle recommend_reason for GitHub data
      let recommendationsArray: string[];
      if (Array.isArray(githubData.recommend_reason)) {
        recommendationsArray = githubData.recommend_reason.filter((reason: string) => reason.trim());
      } else if (typeof githubData.recommend_reason === 'string' && githubData.recommend_reason) {
        const sentences = githubData.recommend_reason.match(/[^.!?]+[.!?]+/g);
        recommendationsArray = sentences ? sentences.map((s: string) => s.trim()) : [githubData.recommend_reason.trim()];
        if (recommendationsArray.length === 0 && githubData.recommend_reason.trim()) {
          recommendationsArray = [githubData.recommend_reason.trim()];
        }
      } else {
        recommendationsArray = ['No specific recommendation reason provided.'];
      }

      return {
        id: githubData.login,
        name: githubData.name || githubData.login,
        positionTitle: githubData.position, // 对于GitHub用户，显示position
        institution: '', // 不显示机构信息
        avatarUrl: githubData.avatar_url,
        skills: Array.isArray(githubData.tags) ? githubData.tags : [],
        featuredWork: {
          title: githubData.repository?.name || 'Featured Repository',
          venue: githubData.repository?.stars ? `⭐ ${formatNumber(githubData.repository.stars)} stars` : 'GitHub',
          type: 'GitHub Repository',
          year: new Date().getFullYear().toString()
        },
        recommendations: recommendationsArray,
        matchScore: githubData.score,
        author_ids: githubData.login, // 使用GitHub用户名作为ID
      };
    } else {
      // 处理paper类型的数据
      const apiPaper = item.data as NewRecommendedPaper;
      const authorInfo = apiPaper.author_info;
    
      // Handle recommend_reason for paper data
    let recommendationsArray: string[];
    if (Array.isArray(authorInfo.recommend_reason)) {
        recommendationsArray = authorInfo.recommend_reason.filter((reason: string) => reason.trim());
    } else if (typeof authorInfo.recommend_reason === 'string' && authorInfo.recommend_reason) {
      const sentences = authorInfo.recommend_reason.match(/[^.!?]+[.!?]+/g);
      recommendationsArray = sentences ? sentences.map((s: string) => s.trim()) : [authorInfo.recommend_reason.trim()];
      if (recommendationsArray.length === 0 && authorInfo.recommend_reason.trim()) {
        recommendationsArray = [authorInfo.recommend_reason.trim()];
      }
    } else {
      recommendationsArray = ['No specific recommendation reason provided.'];
    }

    return {
      id: authorInfo.author_id,
      name: authorInfo.author,
      positionTitle: authorInfo.position,
        institution: '', // 不显示机构信息
      avatarUrl: authorInfo.avatar_url,
      skills: Array.isArray(apiPaper.tags) ? apiPaper.tags : [],
      featuredWork: {
        title: apiPaper.title,
          venue: apiPaper.source,
        type: apiPaper.status,
        year: apiPaper.year
      },
      recommendations: recommendationsArray,
        matchScore: authorInfo.score,
        author_ids: authorInfo.author_id,
    };
    }
  }).sort((a, b) => {
    // 按照matchScore降序排列（分数高的在前）
    if (b.matchScore !== a.matchScore) {
      return b.matchScore - a.matchScore;
    }
    // 如果分数相同，按名字字母顺序排列
    return a.name.localeCompare(b.name);
  });
});

const currentDisplayCandidate = computed<DisplayCandidate | null>(() => {
  return displayCandidates.value[currentIndex.value] || null;
});

// --- Navigation ---
const prevCandidate = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
  }
};

const nextCandidate = () => {
  if (currentIndex.value < displayCandidates.value.length - 1) {
    currentIndex.value++;
  } else {
    // Optional: Fetch more if at the end and more might be available
    // const currentOffset = rawApiResponse.value?.data.authors.length || 0;
    // fetchRecommendations(currentOffset); // This would append or replace, needs more logic
    console.log("At the end of current batch.");
  }
};

// --- Image Error Handling ---
const handleImageError = (event: Event) => {
  const imgElement = event.target as HTMLImageElement
  console.log('handle img error')
  imgElement.src = '/image/avator.png'
}

// --- GitHub Repository Helper Functions ---
const isGitHubRepository = (candidate: DisplayCandidate): boolean => {
  // 检查type是否为GitHub Repository或者venue包含stars（GitHub特征）
  return candidate.featuredWork?.type === 'GitHub Repository' || 
         (candidate.featuredWork?.venue?.includes('stars') ?? false)
}

const getGitHubRepoUrl = (candidate: DisplayCandidate): string => {
  // 仓库名已经包含用户名，格式为 username/repository-name
  const repoFullName = candidate.featuredWork?.title || ''
  
  // 确保仓库全名存在
  if (!repoFullName) {
    return '#'
  }
  
  return `https://github.com/${repoFullName}`
}

const getDisplayTitle = (title: string): string => {
  // 如果标题以斜杠开头，去掉开头的斜杠
  return title.startsWith('/') ? title.substring(1) : title
}

// --- Actions ---
const handleAnalyzeAction = () => {
  if (currentDisplayCandidate.value) {
    console.log("Analyze candidate:", currentDisplayCandidate.value.name, currentDisplayCandidate.value.id);
    
    // 根据候选人的数据类型决定分析路由
    // 需要从原始API数据中获取data_type信息
    const candidate = currentDisplayCandidate.value;
    const encodedId = encodeURIComponent(candidate.author_ids);
    
    // 根据author_ids格式判断数据类型，然后选择对应的分析页面
    if (candidate.author_ids.startsWith('~')) {
      // OpenReview ID，使用scholar分析页面
      router.push(`/report?query=${encodedId}`);
    } else {
      // GitHub login，使用github分析页面
      router.push(`/github?user=${encodedId}`);
    }
  }
}

const fetchNewRecommendations = () => {
  // 在当前已有的候选人中随机选择一个，而不是重新搜索
  if (displayCandidates.value.length > 1) {
    let newIndex;
    do {
      newIndex = Math.floor(Math.random() * displayCandidates.value.length);
    } while (newIndex === currentIndex.value); // 确保不选择当前正在显示的候选人
    
    currentIndex.value = newIndex;
    console.log("Switched to candidate:", displayCandidates.value[newIndex].name);
  } else {
    console.log("Only one candidate available, cannot switch to another.");
  }
}

// --- Network Modal Logic ---
// NetworkModal now handles its own data fetching

// Watch for external changes if this component were to receive candidates as props
watch(() => props.candidates, (newCandidates) => {
  if (newCandidates && newCandidates.length > 0) {
    currentIndex.value = 0;
    loading.value = false;
    error.value = null;
  }
}, { deep: true });
</script>

<style scoped>
/* Your existing styles here */
.talent-card-container {
  position: relative;
  width: 756px;
  height: 548px;
  margin: 20px auto 0; /* Removed bottom margin */
}

.loading-state, .error-state, .empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2em;
  color: #555;
}
/* ... rest of your styles ... */
.results-card {
  position: relative;
  width: 756px;
  height: 548px;
  background: #FFFFFF;
  border: 1px solid #E0E0E0;
  border-radius: 15px;
  backdrop-filter: blur(34px);
  box-shadow: 0px 4px 8px 0px #00000008;
  z-index: 2;
  padding: 20px; /* 改回20px */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.results-card-bg {
  position: absolute;
  top: calc(548px - 284px + 15px); /* 279px */
  left: 8px;
  width: 740px;
  height: 284px;
  background: #FFFFFF;
  border: 1px solid #E0E0E0;
  border-radius: 15px;
  backdrop-filter: blur(34px);
  box-shadow: 0px 4px 8px 0px #00000008;
  opacity: 0.7;
  z-index: 1;
  box-sizing: border-box;
}

.results-card-bg2 {
  position: absolute;
  top: calc(548px - 284px + 15px + 284px - 282px + 15px); /* 294px */
  left: 16px;
  width: 724px;
  height: 282px;
  background: #FFFFFF;
  border: 1px solid #E0E0E0;
  border-radius: 15px;
  backdrop-filter: blur(34px);
  box-shadow: 0px 4px 8px 0px #00000008;
  opacity: 0.3;
  z-index: 0;
  box-sizing: border-box;
}

.nav-button {
  position: absolute;
  width: 48px;
  height: 48px;
  background-color: #FFECE5;
  border: 0.96px solid #F8DDD2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #CB7C5D;
  cursor: pointer;
  z-index: 100;
  transition: all 0.3s ease;
}

.nav-button:hover:not(.disabled) {
  background-color: #CB7C5D;
  border-color: #CB7C5D;
  color: #FFFFFF;
}

.nav-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-button.hidden {
  display: none;
}

.prev-button {
  top: calc(548px/2 - 24px);
  left: -72px;
}

.next-button {
  top: calc(548px/2 - 24px);
  right: -72px;
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  /* gap: 24px; Removed to use absolute positioning for sections */
  position: relative; /* For absolute positioning of children */
}

/* 1. 头部区域样式 */
.header-section {
  /* position: absolute; top: 0; left: 0; right: 0; */
  /* No specific height, let content dictate */
  /* display: flex; flex-direction: column; gap: 16px; */
}

.personal-info {
  /* display: flex; gap: 20px; align-items: flex-start; */
  /* Handled by absolute positioning of children */
}

.avatar-container {
  position: absolute;
  top: 0; /* Was 20px relative to card padding, now 0 relative to content */
  left: 0;  /* Was 20px relative to card padding, now 0 relative to content */
  flex-shrink: 0;
}

.avatar-image {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 1.2px solid #FFFFFF; /* Consider dark mode for border */
  object-fit: cover;
}

.dark .avatar-image {
  border: 1.2px solid #27282D; /* Example dark mode border */
}





.basic-info {
  /* flex: 1; */
}

.candidate-name {
  position: absolute;
  top: 6px; /* 调整为26px - 20px padding = 6px */
  left: 102px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
  text-transform: capitalize;
  color: #000000;
  margin: 0;
}

.candidate-title {
  position: absolute;
  top: 31px; /* 调整为51px - 20px = 31px */
  left: 102px;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 12px;
  letter-spacing: 0%;
  color: #3D3D3D;
  margin: 0;
}

.skills-tags {
  position: absolute;
  top: 56px; /* 调整为76px - 20px = 56px */
  left: 102px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  max-width: calc(100% - 102px - 88px - 10px); /* Ensure it doesn't overlap score chart */
}

.skill-tag {
  /* width: 60px; -- let content define width */
  min-width: 60px;
  height: 28px;
  padding: 6px 10px;
  background-color: #FBEAE3;
  border: 0.5px solid #EBD2C9;
  border-radius: 4px;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 160%; /* This makes text too tall for 28px height, adjust line-height or height */
  /* line-height: normal; or line-height: 1; */
  color: #CB7C5D;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
}

/* 2. 研究成果区域样式 */
.research-section {
  position: absolute;
  top: 106px; /* 调整为126px - 20px = 106px */
  left: 0;
  right: 0;
  width: 100%;
  height: 120px;
  background-color: #F8F4F3B2;
  border-radius: 8px;
  padding: 16px;
  box-sizing: border-box;
}

.dark .research-section {
  background-color: #292929B2; /* Example with alpha */
}

.research-title {
  position: absolute;
  top: 16px;
  left: 16px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0%;
  color: #000000;
  margin: 0;
  max-width: calc(100% - 32px); /* prevent overflow */
  white-space: normal;
  overflow: visible;
  word-wrap: break-word;
  max-height: 72px; /* 允许最多3行文本 (24px * 3) */
}

.research-meta {
  position: absolute;
  bottom: 16px;
  left: 16px;
  display: flex;
  gap: 8px;
  align-items: center;
}

.conference-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.conference-name {
  /* min-width: 60px; */
  height: 28px;
  background-color: #E8E2EF;
  border: 0.5px solid #D0BEE1;
  border-radius: 4px;
  padding: 6px 10px;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 12px;
  /* line-height: 160%; */
  color: #68448B;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
}

.paper-type {
  /* width: 100px; -- let content define */
  min-width: 70px; /* Example min-width */
  height: 28px;
  background-color: #FBEAE3;
  border: 0.5px solid #EBD2C9;
  border-radius: 4px;
  padding: 6px 10px;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 12px;
  /* line-height: 160%; */
  color: #CB7C5D;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
}

/* 3. 推荐理由区域样式 */
.recommendation-section {
  position: absolute;
  top: 238px; /* 106px + 120px + 12px = 238px */
  left: 0;
  right: 0;
  width: 100%;
  height: calc(100% - 238px); /* 动态计算高度，从238px到底部 */
  background-color: rgba(250, 247, 246, 0.5);
  border-radius: 8px;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dark .recommendation-section {
  background-color: #292929B2; /* Example with alpha */
}

.recommendation-content {
  width: 100%;
  margin-bottom: 80px; /* 为按钮留出足够空间 */
}

.recommendation-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 20px; /* Was 20px */
  line-height: 130%;
  color: #000000;
  margin: 0 0 12px 0; /* Was 16px */
}

.recommendation-prefix {
  color: #C0C0C0;
  font-size: 16px;
}

.recommendation-name {
  color: #CB7C5D;
  font-size: 24px;
}

.recommendation-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
  height: 120px; /* 只有列表部分的高度为120px */
  overflow-y: auto; /* 只有列表部分可滚动 */
}

.recommendation-item {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 170%;
  letter-spacing: 0%;
  color: #5B5B5B;
  position: relative;
  padding-left: 16px;
}

.recommendation-item::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #5B5B5B;
  font-weight: 400; /* Can be bolder */
}

.score-chart {
  position: absolute;
  top: calc(238px + 16px - 240px); /* 推荐理由区域顶部 + 16px - 150px */
  right: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10; /* 确保在顶层 */
}

.match-score-label {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0%;
  color: #4A4A4A;
  margin-top: -14.25px;
  text-align: center;
}

.dark .match-score-label {
  color: #C6C6C6;
}

.score-svg {
  width: 88px;
  height: 66px;
}

/* 4. 操作区域样式 */
.action-section {
  position: absolute;
  bottom: 20px; /* 距离推荐区域底部20px，相当于距离主卡片底部40px */
  left: 50%;
  transform: translateX(-50%); /* 水平居中 */
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  justify-content: center;
}

.btn-analyze,
.btn-network,
.btn-see-another {
  width: 158px;
  height: 48px;
  border-radius: 80px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  white-space: nowrap;
  box-sizing: border-box;
}

.btn-icon {
  width: 24px;
  height: 24px;
}

.btn-analyze,
.btn-network {
  background-color: #1C1C21;
  color: #FFFFFF;
  /* padding: 16px 32px; -- width/height and flex handles this */
}

.btn-analyze:hover,
.btn-network:hover {
  background-color: #2A2A30;
}

.btn-see-another {
  background-color: transparent;
  color: #000000;
  border: 1px solid #000000;
  /* padding: 16px 20px; */
}

.btn-see-another:hover {
  background-color: #F5F5F5;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #999999;
}

/* Dark mode styles - check for conflicts with new layout */
/* ... (your existing dark mode styles, ensure they still apply correctly) ... */

.dark .results-card {
  background: #141415;
  border: 1px solid #27282D;
  box-shadow: 0px 3px 8px 0px #0000001A;
}

/* 姓名 */
.dark .candidate-name {
  color: #FAF9F5;
}

/* 简介 */
.dark .candidate-title {
  color: #7A7A7A;
}

/* 标签 */
.dark .skill-tag {
  background-color: #323232;
  border: 0.5px solid #3E3E3E;
  color: #C6C6C6;
}

/* 研究成果部分 */
.dark .research-section {
  background-color: #292929;
}

.dark .research-title {
  color: #FAF9F5;
}

/* 第一和第三个标签（技能标签和论文类型） */
.dark .paper-type {
  background-color: #323232;
  border: 0.5px solid #3E3E3E;
  color: #C6C6C6;
}

/* 第二个标签（会议名称） */
.dark .conference-name {
  background-color: #423C47;
  border: 0.5px solid #554B5E;
  color: #A191B0;
}

/* 推荐部分 */
.dark .recommendation-section {
  background-color: #292929;
}

.dark .recommendation-title {
  color: #FAF9F5;
}

.dark .recommendation-prefix {
  color: #C0C0C0;
}

.dark .recommendation-name {
  color: #CB7C5D;
}

.dark .recommendation-item {
  color: #C6C6C6;
}

.dark .recommendation-item::before {
  color: #C6C6C6;
}

/* 装饰卡片 */
.dark .results-card-bg {
  background: #252525;
  border: 1px solid #27282D;
  backdrop-filter: blur(34px);
  box-shadow: 0px 4px 8px 0px #00000008;
}

.dark .results-card-bg2 {
  background: #2F2F2F;
  border: 1px solid #27282D;
  backdrop-filter: blur(34px);
  box-shadow: 0px 4px 8px 0px #00000008;
}

/* 按钮图标控制 */
.btn-icon-dark {
  display: none;
}

.dark .btn-icon-light {
  display: none;
}

.dark .btn-icon-dark {
  display: inline-block;
}

/* 按钮样式 */
.dark .btn-analyze,
.dark .btn-network {
  background-color: #FAF9F5;
  color: #30302E;
  border: none;
}

.dark .btn-analyze:hover,
.dark .btn-network:hover {
  background-color: #F0F0F0;
}

.dark .btn-see-another {
  background-color: transparent;
  border: 1px solid #323232;
  color: #FAF9F5;
}

.dark .btn-see-another:hover {
  background-color: rgba(50, 50, 50, 0.1);
}

/* 导航按钮 */
.dark .nav-button {
  background-color: #292929;
  border: 0.96px solid #3E3E3E;
  color: #FAF9F5;
}

.dark .nav-button:hover:not(.disabled) {
  background-color: #CB7C5D;
  border-color: #CB7C5D;
  color: #FFFFFF;
}
</style>