<template>
  <div v-if="show" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="relative">
      <div
        class="rounded-2xl shadow-xl p-6 w-[1200px] h-[700px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1] dark:from-[#141415] dark:to-[#141415] border border-transparent dark:border-[#27282D]"
        data-card-id="share-card-github"
      >
        <div class="bg-[url(/image/graphbg.png)] bg-right bg-contain bg-no-repeat">
          <div class="w-[850px]">
            <!-- 顶部用户信息 -->
            <div class="flex items-center justify-start h-[80px]" v-if="user">
              <img :src="user.avatar" class="w-20 h-20 rounded-full mr-4" />
              <div class="flex flex-col flex-1 justify-around">
                <div class="flex items-center gap-4">
                  <h2 class="text-xl font-bold whitespace-nowrap truncate max-w-[300px]">{{ user.name }}</h2>
                  <div class="flex items-start gap-1 text-sm text-gray-500 flex-1">
                    <SvgIcon name="verified" class="mt-0.5 flex-shrink-0" />
                    <span class="line-clamp-2">{{ user.bio || user.role }}</span>
                  </div>
                </div>
                <div class="flex items-center" v-if="user?.login">
                  <a 
                    :href="`https://github.com/${user.login}`" 
                    target="_blank"
                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm transition-colors"
                  >
                    www.github.com/{{ user.login }}
                  </a>
                </div>
              </div>
            </div>

            <div class="flex items-center justify-between mt-6 gap-4">
              <!-- 饼图区域 -->
              <div
                class="flex flex-col justify-between p-4 flex-1 h-[475px] w-[450px] border border-gray-200 dark:border-[#27282D] bg-[#FFFFFF]/60 dark:bg-[#14141580] shadow-lg"
                style="backdrop-filter: blur(14px); border-radius: 15px;"
              >
                <div>
                  <div class="fx-cer font-700 gap-2 mb-4"><SvgIcon name="research" />Overview</div>
                  <div class="mb-4">
                    <SegmentTable :items="insightsItems || []" />
                  </div>
                  <!-- GitHub Language Distribution -->
                  <div class="mb-4">
                    <div class="h-[180px] w-full">
                      <GithubDonut 
                        :languages="languageData" 
                        :total="languageTotal" 
                        :compact="true"
                      />
                    </div>
                  </div>
                </div>
                <div class="fx-cer justify-between gap-2">
                  <div
                    class="w-[190px] h-[106px] custom-bg bg-[url(/image/sharecard/Group2x1.png)] dark:bg-[url(/image/sharecard/Group2xdark.png)] bg-no-repeat bg-contain bg-left-top p-3 flex flex-col justify-between"
                    style="border-radius: 4px;"
                  >
                    <div class="fx-cer gap-1">
                      <span class="fx-cer justify-center w-6 h-6 rounded-1 bg-[#D0D5E3]"
                        ><SvgIcon name="add1" /></span
                      ><span class="text-[#5F6D94] dark:text-[#A5AEC6] font-700">Additions</span>
                    </div>
                    <div class="text-[#5F6D94] dark:text-[#A5AEC6] ml-4 font-600 text-3xl">
                      + {{ formatNumber(additions || 258493) }}
                    </div>
                  </div>
                  <div
                    class="w-[190px] h-[106px] custom-bg bg-[url(/image/sharecard/Group2x.png)] dark:bg-[url(/image/sharecard/Group2xdark.png)] bg-no-repeat bg-contain bg-left-top p-3 flex flex-col justify-between"
                    style="border-radius: 4px;"
                  >
                    <div class="fx-cer gap-1">
                      <span class="fx-cer justify-center w-6 h-6 rounded-1 bg-[#F1D5CB]"
                        ><SvgIcon name="trash-bin" /></span
                      ><span class="text-[#CB7C5D] dark:text-[#B28383] font-700">Deletions</span>
                    </div>
                    <div class="text-[#CB7C5D] dark:text-[#B28383] ml-4 font-600 text-3xl">
                      - {{ formatNumber(deletions || 192148) }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 年收入和榜样信息 -->
              <div
                class="flex flex-col justify-between h-[475px] w-[450px] p-4 flex-1 border bg-[#FFFFFF]/60 border-gray-200 dark:border-[#27282D] dark:bg-[#14141580] shadow-lg"
                style="backdrop-filter: blur(14px); border-radius: 15px;"
              >
                <div>
                  <div class="fx-cer font-700 gap-2 mb-4">
                    <SvgIcon name="project" />Highlight
                  </div>
                  <div class="flex flex-col gap-4">
                    <div
                      class="border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-4 py-4 rounded-1 dark:bg-[#222222] dark:text-white"
                    >
                      <div
                        class="font-3.4 font-700 leading-tight border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] dark:text-white pb-1 mb-2"
                      >
                        <a :href="featureProject?.url" target="_blank" class="hover:underline hover:text-[#CB7C5D] transition-colors">
                          {{ featureProject?.name || 'FastAI-Transformer' }}
                        </a>
                      </div>
                      <div class="text-3.5 font-400 leading-5 text-[#4D4846] dark:text-white mb-2 line-clamp-2">
                        {{ featureProject?.description || 'A high-performance transformer implementation for fast AI model training and inference.' }}
                      </div>
                      <div class="flex items-center gap-6">
                        <div class="flex items-center gap-2">
                          <div class="fx-cer justify-center h-5 w-5 rounded-1 bg-[#EEDFD9]">
                            <SvgIcon name="stars" class="text-3" />
                          </div>
                          <span class="text-[#969696] dark:text-[#7A7A7A] text-3">{{ featureProject?.stargazerCount?.toLocaleString() || '5892' }}</span>
                          <span class="text-[#969696] dark:text-[#7A7A7A] text-2.5">Stars</span>
                        </div>
                        <div class="flex items-center gap-2">
                          <div class="fx-cer justify-center h-5 w-5 rounded-1 bg-[#EEDFD9]">
                            <SvgIcon name="forks" class="text-3" />
                          </div>
                          <span class="text-[#969696] dark:text-[#7A7A7A] text-3">{{ featureProject?.forkCount?.toLocaleString() || '1249' }}</span>
                          <span class="text-[#969696] dark:text-[#7A7A7A] text-2.5">Forks</span>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Most Valuable Pull Request -->
                    <div
                      class="border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-4 py-3 rounded-1 dark:bg-[#222222] dark:text-white"
                    >
                      <div
                        class="text-3 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-5 pb-2 dark:text-white"
                      >
                        <a :href="mostValuablePR?.url" target="_blank" class="hover:underline hover:text-[#CB7C5D] transition-colors">
                          {{ mostValuablePR?.title || 'Enhanced ML Pipeline Performance' }}
                        </a>
                      </div>
                      <div class="text-3 font-400 leading-5 text-[#4D4846] py-1 dark:text-[#7A7A7A] overflow-hidden" style="display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2;">
                        {{ mostValuablePR?.impact || 'Significantly improved model training efficiency and reduced computational overhead' }}
                      </div>
                      <div class="text-2.5 text-[#969696] dark:text-[#7A7A7A]">
                        {{ mostValuablePR?.repository || 'tensorflow/tensorflow' }}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="fx-cer justify-between gap-2">
                  <div
                    class="w-[190px] h-[106px] custom-bg bg-[url(/image/sharecard/Group2x1.png)] dark:bg-[url(/image/sharecard/Group2xdark.png)] bg-no-repeat bg-contain bg-left-top p-3 flex flex-col justify-between"
                    style="border-radius: 4px;"
                  >
                    <div class="fx-cer gap-1">
                      <span class="fx-cer justify-center w-6 h-6 rounded-1 bg-[#D0D5E3]"
                        ><SvgIcon name="growth" /></span
                      ><span class="text-[#5F6D94] dark:text-[#A5AEC6] font-700">Market Value <span class="text-xs">($)</span></span>
                    </div>
                    <div class="text-[#5F6D94] dark:text-[#A5AEC6] font-600 text-3xl text-center">
                      {{ formatMinSalaryWithSuffix(valuationLevel?.salary_range || '$120K - $180K') }}
                    </div>
                  </div>
                  <div
                    class="w-[190px] h-[106px] custom-bg bg-[url(/image/sharecard/Group2x.png)] dark:bg-[url(/image/sharecard/Group2xdark.png)] bg-no-repeat bg-contain bg-left-top p-3 flex flex-col justify-between"
                    style="border-radius: 4px;"
                  >
                    <div class="fx-cer gap-1">
                      <span class="fx-cer justify-center w-6 h-6 rounded-1 bg-[#F1D5CB]"
                        ><SvgIcon name="growth-investing" /></span
                      ><span class="text-[#CB7C5D] dark:text-[#B28383] font-700">YoE</span>
                    </div>
                    <div class="text-[#CB7C5D] dark:text-[#B28383] font-600 text-3xl text-center">
                      {{ workExperience || '5' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <img
              :src="user?.avatar || '@/assets/image/avator.png'"
              alt=""
              class="absolute top-[336px] right-[197px] w-[65px] h-[65px] rounded-full"
            />
          </div>
          <div class="fx-cer justify-between border-t dark:border-[#323232] h-[70px] mt-7.5">
            <img
              :src="isDark ? '/image/darklogo2.png' : '/image/newlogo1.png'"
              width="95"
              height="42"
              :alt="isDark ? 'DINQ dark logo' : 'DINQ logo'"
            />
            <div class="flex items-center gap-3" data-action-buttons>
              <!-- Download Button -->
              <button
                class="fx-cer bg-[#FFFFFF]/60 dark:bg-[#14141580] border border-gray-200 dark:border-[#27282D] rounded-full py-2 px-4 text-black dark:text-white gap-2 transition-all duration-200 select-none dark:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed hover:bg-[#F5F5F5] dark:hover:bg-[#27282D] disabled:hover:bg-[#FFFFFF]/60 dark:disabled:hover:bg-[#14141580] min-h-[40px] cursor-pointer"
                :disabled="isDownloading"
                @click="handleDownload"
                style="backdrop-filter: blur(34px);"
              >
                <div
                  v-if="isDownloading"
                  class="animate-spin w-4 h-4 border-2 border-gray-400 dark:border-gray-300 border-t-transparent rounded-full pointer-events-none"
                ></div>
                <div v-else class="i-material-symbols:download w-4 h-4 pointer-events-none"></div>
                <span class="text-sm font-medium pointer-events-none">{{ isDownloading ? 'Downloading...' : 'Download' }}</span>
              </button>

              <!-- Share Button -->
              <ShareButton card-id="share-card-github" :is-dark="isDark" variant="transparent" />
            </div>
          </div>
        </div>
      </div>

      <button
        class="absolute top-4 right-4 p-2 rounded-full transition-colors bg-transparent dark:bg-[#141415] hover:bg-black/10 dark:hover:bg-white/10"
        @click="$emit('close')"
      >
        <div class="i-carbon:close text-xl text-gray-500"></div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps, computed, ref } from 'vue'
  import html2canvas from 'html2canvas-pro'
  import SvgIcon from '../SvgIcon/index.vue'
  import GithubDonut from '../GithubDonut/index.vue'
  import ShareButton from '../ShareButton/index.vue'

  interface User {
    name: string;
    avatar: string;
    role: string;
    login?: string;
    bio?: string;
  }

  interface Stats {
    repositories: number;
    stars: number;
    pullRequests: number;
  }

  interface RoleModel {
    name: string;
    avatar: string;
    title: string;
    achievement: string;
  }

  interface InsightItem {
    label: string;
    value: string | number;
  }

  interface FeatureProject {
    name: string;
    description: string;
    url: string;
    stargazerCount: number;
    forkCount: number;
    contributors: number;
    used_by: number;
    monthly_trending: number;
  }

  interface ValuationLevel {
    salary_range: string | number[];
    level: string;
    industry_ranking?: string | number;
    growth_potential?: string;
    reasoning?: string;
    total_compensation?: string;
  }

  interface MostValuablePR {
    title: string;
    url: string;
    repository: string;
    impact: string;
  }

  const props = defineProps<{
    show: boolean;
    user?: User;
    stats?: Stats;
    income?: number;
    roleModel?: RoleModel;
    isDark?: boolean;
    insightsItems?: InsightItem[];
    languages?: Record<string, number>;
    languageTotal?: number;
    featureProject?: FeatureProject;
    additions?: number;
    deletions?: number;
    valuationLevel?: ValuationLevel;
    workExperience?: number;
    mostValuablePR?: MostValuablePR;
  }>()

  const isDownloading = ref(false)

  // 下载功能
  const handleDownload = async () => {
    if (isDownloading.value || !process.client) return

    isDownloading.value = true
    try {
      const elementToCapture = document.querySelector('[data-card-id="share-card-github"]')
      if (!elementToCapture) {
        throw new Error('GitHub share card element not found')
      }

      // 等待一小段时间确保所有元素都渲染完成
      await new Promise(resolve => setTimeout(resolve, 500))

      // 使用html2canvas-pro，它对现代CSS有更好的支持
      const canvas = await html2canvas(elementToCapture as HTMLElement, {
        backgroundColor: '#ffffff',
        scale: 2,
        useCORS: true,
        allowTaint: true,
        logging: false,
        imageTimeout: 15000,
        // html2canvas-pro有更好的SVG和CSS支持，所以简化配置
        foreignObjectRendering: false,
        // 确保元素完全在视图中
        scrollX: 0,
        scrollY: 0,
        // 在截图时替换按钮为版权信息
        onclone: (clonedDoc) => {
          const clonedElement = clonedDoc.querySelector('[data-card-id="share-card-github"]')
          if (clonedElement) {
            // 查找按钮容器并替换为版权信息和二维码
            const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
            if (buttonContainer) {
              // 创建右下角信息容器（版权信息+二维码）
              const bottomRightContainer = clonedDoc.createElement('div')
              bottomRightContainer.style.cssText = 'position: absolute; bottom: 12px; right: 16px; display: flex; align-items: center; gap: 8px; z-index: 10;'

              // 创建版权信息元素
              const copyrightDiv = clonedDoc.createElement('div')
              copyrightDiv.className = 'text-3.5 font-400'
              copyrightDiv.style.fontSize = '12px'
              copyrightDiv.style.color = props.isDark ? '#7A7A7A' : '#666'
              copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'

              // 创建二维码元素
              const qrCode = clonedDoc.createElement('img')
              qrCode.src = '/image/qrcode.png'
              qrCode.alt = 'QR Code'
              qrCode.style.cssText = 'width: 48px; height: 48px; flex-shrink: 0;'

              // 将版权信息和二维码添加到右下角容器
              bottomRightContainer.appendChild(copyrightDiv)
              bottomRightContainer.appendChild(qrCode)

              // 替换按钮容器为右下角容器
              buttonContainer.parentNode?.replaceChild(bottomRightContainer, buttonContainer)
            }

            // 替换SVG图标为PNG图片以解决html2canvas兼容性问题
            const svgIconElements = clonedElement.querySelectorAll('svg.svg-icon')
            svgIconElements.forEach((svgEl) => {
              const svgElement = svgEl as SVGElement
              const useElement = svgElement.querySelector('use')
              if (!useElement) return
              
              const iconId = useElement.getAttribute('xlink:href') || useElement.getAttribute('href')
              if (!iconId) return
              
              const imgElement = clonedDoc.createElement('img')
              
              // 根据图标ID确定要使用的图片
              if (iconId === '#icon-verified') {
                imgElement.src = '/image/sharecard/github-verify.png'
                imgElement.alt = 'verified'
                imgElement.className = 'w-4 h-4 mt-0.5 flex-shrink-0'
              } else if (iconId === '#icon-research') {
                imgElement.src = '/image/sharecard/overview.png'
                imgElement.alt = 'overview'
                imgElement.className = 'w-5 h-5'
              } else if (iconId === '#icon-add1') {
                imgElement.src = '/image/sharecard/additions.png'
                imgElement.alt = 'additions'
                imgElement.className = 'w-5 h-5'
              } else if (iconId === '#icon-trash-bin') {
                imgElement.src = '/image/sharecard/deletions.png'
                imgElement.alt = 'deletions'
                imgElement.className = 'w-5 h-5'
              } else if (iconId === '#icon-project') {
                imgElement.src = '/image/sharecard/highlight.png'
                imgElement.alt = 'highlight'
                imgElement.className = 'w-5 h-5'
              } else if (iconId === '#icon-stars') {
                imgElement.src = '/image/sharecard/stars.png'
                imgElement.alt = 'stars'
                imgElement.className = 'w-4 h-4'
              } else if (iconId === '#icon-forks') {
                imgElement.src = '/image/sharecard/forks.png'
                imgElement.alt = 'forks'
                imgElement.className = 'w-4 h-4'
              } else if (iconId === '#icon-growth') {
                imgElement.src = '/image/sharecard/marketvalue.png'
                imgElement.alt = 'market value'
                imgElement.className = 'w-5 h-5'
              } else if (iconId === '#icon-growth-investing') {
                imgElement.src = '/image/sharecard/yoe.png'
                imgElement.alt = 'yoe'
                imgElement.className = 'w-5 h-5'
              }
              
              // 替换SVG为IMG
              if (imgElement.src) {
                svgElement.parentNode?.replaceChild(imgElement, svgElement)
              }
            })

            // 替换头像的fallback路径
            const avatarImages = clonedElement.querySelectorAll('img[src*="@/assets/image/avator.png"]')
            avatarImages.forEach(img => {
              const imgEl = img as HTMLImageElement
              imgEl.src = '/image/avator.png'
            })

            // 修复深色模式下的边框颜色和高亮卡片样式
            const isDarkMode = props.isDark

            // 修复小卡片的背景图片显示
            const customBgCards = clonedElement.querySelectorAll('.custom-bg')
            customBgCards.forEach((card) => {
              const cardEl = card as HTMLElement

              // 根据卡片内容确定背景图片类型
              const isAdditionsCard = cardEl.textContent?.includes('Additions') || cardEl.querySelector('img[alt="additions"]')
              const isDeletionsCard = cardEl.textContent?.includes('Deletions') || cardEl.querySelector('img[alt="deletions"]')
              const isMarketValueCard = cardEl.textContent?.includes('Market Value') || cardEl.querySelector('img[alt="market value"]')
              const isYoECard = cardEl.textContent?.includes('YoE') || cardEl.querySelector('img[alt="yoe"]')

              // 强制设置背景图片，使用!important确保优先级
              if (isDarkMode) {
                // 深色模式统一使用深色背景
                cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2xdark.png)', 'important')
              } else {
                // 亮色模式根据卡片类型使用不同背景
                if (isAdditionsCard || isMarketValueCard) {
                  // Additions和Market Value使用Group2x1.png
                  cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2x1.png)', 'important')
                } else if (isDeletionsCard || isYoECard) {
                  // Deletions和YoE使用Group2x.png
                  cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2x.png)', 'important')
                }
              }

              // 确保其他背景属性正确设置
              cardEl.style.setProperty('background-repeat', 'no-repeat', 'important')
              cardEl.style.setProperty('background-size', 'contain', 'important')
              cardEl.style.setProperty('background-position', 'left top', 'important')

              // 修复文字颜色以匹配背景图片
              const textElements = cardEl.querySelectorAll('[class*="text-[#"]')
              textElements.forEach(textEl => {
                const textElement = textEl as HTMLElement
                if (isDarkMode) {
                  // 深色模式下的文字颜色
                  if (isAdditionsCard || isMarketValueCard) {
                    // 蓝色系卡片 - 深色模式使用较亮的蓝色
                    if (textElement.classList.contains('text-[#5F6D94]') || textElement.classList.contains('dark:text-[#A5AEC6]')) {
                      textElement.style.setProperty('color', '#A5AEC6', 'important')
                    }
                  } else if (isDeletionsCard || isYoECard) {
                    // 橙色系卡片 - 深色模式使用较亮的橙色
                    if (textElement.classList.contains('text-[#CB7C5D]') || textElement.classList.contains('dark:text-[#B28383]')) {
                      textElement.style.setProperty('color', '#B28383', 'important')
                    }
                  }
                } else {
                  // 浅色模式下的文字颜色
                  if (isAdditionsCard || isMarketValueCard) {
                    // 蓝色系卡片 - 确保文字颜色正确
                    if (textElement.classList.contains('text-[#5F6D94]')) {
                      textElement.style.setProperty('color', '#5F6D94', 'important')
                    }
                  } else if (isDeletionsCard || isYoECard) {
                    // 橙色系卡片 - 确保文字颜色正确
                    if (textElement.classList.contains('text-[#CB7C5D]')) {
                      textElement.style.setProperty('color', '#CB7C5D', 'important')
                    }
                  }
                }
              })
            })

            // 修复毛玻璃效果卡片的背景和样式
            const glassCards = clonedElement.querySelectorAll('[style*="backdrop-filter: blur(14px)"]')
            glassCards.forEach(card => {
              const cardEl = card as HTMLElement
              if (isDarkMode) {
                // 深色模式：增强背景不透明度以模拟毛玻璃效果
                cardEl.style.backgroundColor = 'rgba(20, 20, 21, 0.85)'
                cardEl.style.backdropFilter = 'none' // 移除backdrop-filter，因为html2canvas不支持
                cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)'
              } else {
                // 亮色模式：增强背景不透明度以模拟毛玻璃效果
                cardEl.style.backgroundColor = 'rgba(255, 255, 255, 0.85)'
                cardEl.style.backdropFilter = 'none' // 移除backdrop-filter，因为html2canvas不支持
                cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)'
              }
            })

            // 修复高亮卡片的背景颜色和边框（适用于所有模式）
            const highlightCards = clonedElement.querySelectorAll('.border-l-4')
            highlightCards.forEach(card => {
              const cardEl = card as HTMLElement
              if (isDarkMode) {
                // 深色模式样式
                cardEl.style.backgroundColor = '#222222'
                cardEl.style.borderLeftColor = '#654D43'
                cardEl.style.borderLeftWidth = '4px'
                cardEl.style.borderLeftStyle = 'solid'
              } else {
                // 亮色模式样式
                cardEl.style.backgroundColor = '#FAF2EF'
                cardEl.style.borderLeftColor = '#CB7C5D'
                cardEl.style.borderLeftWidth = '4px'
                cardEl.style.borderLeftStyle = 'solid'
              }

              // 修复高亮卡片内的分割线颜色
              const dividers = cardEl.querySelectorAll('.border-b-1')
              dividers.forEach(divider => {
                const dividerEl = divider as HTMLElement
                if (isDarkMode) {
                  dividerEl.style.borderBottomColor = '#3E3E3E'
                } else {
                  dividerEl.style.borderBottomColor = '#F2E8E4'
                }
              })
            })

            if (isDarkMode) {
              // 修复所有卡片的边框颜色
              const cardElements = clonedElement.querySelectorAll('.border-gray-200')
              cardElements.forEach(card => {
                (card as HTMLElement).style.borderColor = '#27282D'
              })

              // 修复主卡片的边框颜色
              const mainCard = clonedElement.querySelector('[data-card-id="share-card-github"] > div')
              if (mainCard) {
                (mainCard as HTMLElement).style.borderColor = '#27282D'
              }

              // 修复卡片标题文字颜色
              const titleElements = clonedElement.querySelectorAll('.text-black')
              titleElements.forEach(title => {
                (title as HTMLElement).style.color = '#FAF9F5'
              })
            }
          }
        }
      })

      // 创建下载链接
      const link = document.createElement('a')
      link.download = `dinq-github-analysis-${Date.now()}.png`
      link.href = canvas.toDataURL('image/png', 1.0)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Download failed:', error)
      // 提供备用方案提示
      alert('Screenshot failed. This might be due to CORS issues with external images. Please try again or contact support.')
    } finally {
      isDownloading.value = false
    }
  }

  // 为GithubDonut组件提供语言数据
  const languageData = computed(() => props.languages || {})
  const languageTotal = computed(() => props.languageTotal || 0)

  // 格式化数字，超过1K时显示为K单位，超过1M时显示为M单位
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(0) + 'K'
    }
    return num.toString()
  }

  // 格式化薪资范围，自动转换为K/M单位
  const formatSalaryRange = (salaryRange: string | number[]): string => {
    // 如果是数组格式，直接处理
    if (Array.isArray(salaryRange)) {
      const formatSalaryValue = (value: number): string => {
        if (value >= 1000000) {
          return (value / 1000000).toFixed(1) + 'M';
        }
        if (value >= 1000) {
          return (value / 1000) + 'K';
        }
        return value.toString();
      };

      return `$${formatSalaryValue(salaryRange[0])} ~ $${formatSalaryValue(salaryRange[1])}`;
    }

    // 如果是字符串格式，使用原来的逻辑
    const rangeMatch = salaryRange.match(/\$(\d+(?:,\d{3})*(?:[KkMm])?)\s*-\s*\$(\d+(?:,\d{3})*(?:[KkMm])?)/);

    if (rangeMatch) {
      const [, minStr, maxStr] = rangeMatch;

      const formatSalaryValue = (value: string): string => {
        // 如果已经包含K或M，直接返回
        if (value.includes('K') || value.includes('k')) {
          return value.toUpperCase();
        }
        if (value.includes('M') || value.includes('m')) {
          return value.toUpperCase();
        }

        // 移除逗号并转换为数字
        const num = parseInt(value.replace(/,/g, ''));

        if (num >= 1000000) {
          return (num / 1000000).toFixed(1) + 'M';
        }
        if (num >= 1000) {
          return (num / 1000) + 'K';
        }

        return num.toString();
      };

      return `$${formatSalaryValue(minStr)} ~ $${formatSalaryValue(maxStr)}`;
    }

    // 如果格式不匹配，返回原始值
    return salaryRange;
  }

  // 格式化薪资最低值，只返回数字部分（不包含$和K/M）
  const formatMinSalary = (salaryRange: string | number[]): string => {
    // 如果是数组格式，直接处理
    if (Array.isArray(salaryRange)) {
      const minValue = salaryRange[0];
      if (minValue >= 1000000) {
        return (minValue / 1000000).toFixed(1);
      }
      if (minValue >= 1000) {
        return (minValue / 1000).toString();
      }
      return minValue.toString();
    }

    // 如果是字符串格式，提取最低值
    const rangeMatch = salaryRange.match(/\$(\d+(?:,\d{3})*(?:[KkMm])?)/);

    if (rangeMatch) {
      const minStr = rangeMatch[1];

      // 如果已经包含M，移除M并返回数字
      if (minStr.includes('M') || minStr.includes('m')) {
        return minStr.replace(/[Mm]/g, '');
      }

      // 如果已经包含K，移除K并返回数字
      if (minStr.includes('K') || minStr.includes('k')) {
        return minStr.replace(/[Kk]/g, '');
      }

      // 移除逗号并转换为数字，然后转换为K/M单位
      const num = parseInt(minStr.replace(/,/g, ''));

      if (num >= 1000000) {
        return (num / 1000000).toFixed(1);
      }
      if (num >= 1000) {
        return (num / 1000).toString();
      }

      return num.toString();
    }

    // 如果格式不匹配，返回默认值
    return '120';
  }

  // 格式化薪资最低值，包含K/M后缀
  const formatMinSalaryWithSuffix = (salaryRange: string | number[]): string => {
    // 如果是数组格式，直接处理
    if (Array.isArray(salaryRange)) {
      const minValue = salaryRange[0];
      if (minValue >= 1000000) {
        return (minValue / 1000000).toFixed(1) + 'M';
      }
      if (minValue >= 1000) {
        return (minValue / 1000).toString() + 'K';
      }
      return minValue.toString();
    }

    // 如果是字符串格式，提取最低值
    const rangeMatch = salaryRange.match(/\$(\d+(?:\.\d+)?(?:,\d{3})*(?:[KkMm])?)/);

    if (rangeMatch) {
      const minStr = rangeMatch[1];

      // 如果已经包含M，直接返回
      if (minStr.includes('M') || minStr.includes('m')) {
        return minStr.replace(/[Mm]/g, 'M');
      }

      // 如果已经包含K，直接返回
      if (minStr.includes('K') || minStr.includes('k')) {
        return minStr.replace(/[Kk]/g, 'K');
      }

      // 移除逗号并转换为数字，然后转换为K/M单位
      const num = parseInt(minStr.replace(/,/g, ''));

      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
      }
      if (num >= 1000) {
        return (num / 1000).toString() + 'K';
      }

      return num.toString();
    }

    // 如果格式不匹配，返回默认值
    return '120K';
  }
</script>

<style scoped>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>